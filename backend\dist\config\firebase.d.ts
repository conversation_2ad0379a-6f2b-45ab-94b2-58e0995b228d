import admin from 'firebase-admin';
export declare const firebaseAdmin: typeof admin;
export declare const db: admin.firestore.Firestore;
export declare const collections: {
    users: string;
    schools: string;
    students: string;
    classes: string;
    smsMessages: string;
    chatMessages: string;
    smsTemplates: string;
};
export declare const getSchoolRef: (schoolId: string) => admin.firestore.DocumentReference<admin.firestore.DocumentData, admin.firestore.DocumentData>;
export declare const getStudentsRef: (schoolId: string) => admin.firestore.CollectionReference<admin.firestore.DocumentData, admin.firestore.DocumentData>;
export declare const getClassesRef: (schoolId: string) => admin.firestore.CollectionReference<admin.firestore.DocumentData, admin.firestore.DocumentData>;
export declare const getSmsMessagesRef: (schoolId: string) => admin.firestore.CollectionReference<admin.firestore.DocumentData, admin.firestore.DocumentData>;
export declare const getChatMessagesRef: (schoolId: string) => admin.firestore.CollectionReference<admin.firestore.DocumentData, admin.firestore.DocumentData>;
//# sourceMappingURL=firebase.d.ts.map