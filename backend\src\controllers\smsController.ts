import { Response } from 'express';
import { AuthenticatedRequest, ValidationError, NotFoundError } from '../types';
import { asyncHandler } from '../middleware/errorHandler';
import { smsService } from '../services/smsService';
import { aiService } from '../services/aiService';
import { db, getSmsMessagesRef, getStudentsRef, getClassesRef } from '../config/firebase';
import type { SendSmsRequest } from '../../../shared/types';
import type { ApiResponse } from '../types';
import { SMS_TEMPLATES } from '../../../shared/types';

// SMS gönderme
export const sendSms = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { recipientType, recipientId, message, useAiEnhancement }: SendSmsRequest = req.body;
  const schoolId = req.params.schoolId;
  const senderId = req.user!.id;

  if (!message || message.trim().length === 0) {
    throw new ValidationError('Message is required');
  }

  if (message.length > 1000) {
    throw new ValidationError('Message is too long (max 1000 characters)');
  }

  let finalMessage = message.trim();
  let originalMessage = message.trim();

  // AI ile mesajı iyileştir
  if (useAiEnhancement) {
    const enhanceResult = await aiService.enhanceMessage(message.trim());
    if (enhanceResult.success) {
      finalMessage = enhanceResult.enhancedMessage;
    }
  }

  // Alıcıları belirle
  const recipients = await getRecipients(schoolId, recipientType, recipientId);
  
  if (recipients.length === 0) {
    throw new ValidationError('No recipients found');
  }

  // SMS'leri gönder
  const smsResults = await smsService.sendBulkSms(
    recipients.map(r => r.phone),
    finalMessage
  );

  // Sonuçları veritabanına kaydet
  const smsRef = getSmsMessagesRef(schoolId);
  const batch = db.batch();

  recipients.forEach((recipient, index) => {
    const result = smsResults[index];
    const smsDoc = smsRef.doc();

    batch.set(smsDoc, {
      schoolId,
      senderId,
      recipientType,
      recipientId: recipientId || null,
      recipientPhone: recipient.phone,
      recipientName: recipient.name,
      message: finalMessage,
      originalMessage: useAiEnhancement ? originalMessage : null,
      status: result.success ? 'sent' : 'failed',
      messageId: result.messageId || null,
      error: result.error || null,
      sentAt: result.success ? new Date() : null,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  });

  await batch.commit();

  const successful = smsResults.filter(r => r.success).length;
  const failed = smsResults.length - successful;

  const response: ApiResponse = {
    success: true,
    data: {
      totalRecipients: recipients.length,
      successful,
      failed,
      message: finalMessage,
      originalMessage: useAiEnhancement ? originalMessage : null,
      enhanced: useAiEnhancement,
    },
    message: `SMS sent to ${successful} recipients successfully`,
  };

  res.json(response);
});

// SMS geçmişini getir
export const getSmsHistory = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const schoolId = req.params.schoolId;
  const limit = parseInt(req.query.limit as string) || 50;
  const page = parseInt(req.query.page as string) || 1;
  const status = req.query.status as string;
  const recipientType = req.query.recipientType as string;

  let query = getSmsMessagesRef(schoolId)
    .orderBy('createdAt', 'desc')
    .limit(limit);

  // Filtreler
  if (status) {
    query = query.where('status', '==', status);
  }
  
  if (recipientType) {
    query = query.where('recipientType', '==', recipientType);
  }

  const snapshot = await query.get();
  
  const messages = snapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data(),
    createdAt: doc.data().createdAt.toDate(),
    sentAt: doc.data().sentAt?.toDate() || null,
    updatedAt: doc.data().updatedAt.toDate(),
  }));

  const response: ApiResponse = {
    success: true,
    data: {
      messages,
      pagination: {
        page,
        limit,
        total: messages.length,
        hasMore: messages.length === limit,
      },
    },
  };

  res.json(response);
});

// SMS istatistikleri
export const getSmsStats = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const schoolId = req.params.schoolId;
  const period = req.query.period as string || '30'; // days

  const periodDays = parseInt(period);
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - periodDays);

  const smsRef = getSmsMessagesRef(schoolId);
  
  const [totalSnapshot, sentSnapshot, failedSnapshot, recentSnapshot] = await Promise.all([
    smsRef.count().get(),
    smsRef.where('status', '==', 'sent').count().get(),
    smsRef.where('status', '==', 'failed').count().get(),
    smsRef.where('createdAt', '>=', startDate).count().get(),
  ]);

  const response: ApiResponse = {
    success: true,
    data: {
      total: totalSnapshot.data().count,
      sent: sentSnapshot.data().count,
      failed: failedSnapshot.data().count,
      recent: recentSnapshot.data().count,
      period: `${periodDays} days`,
      successRate: totalSnapshot.data().count > 0 
        ? ((sentSnapshot.data().count / totalSnapshot.data().count) * 100).toFixed(2)
        : '0',
    },
  };

  res.json(response);
});

// SMS önizleme
export const previewSms = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { message } = req.body;

  if (!message || message.trim().length === 0) {
    throw new ValidationError('Message is required');
  }

  const preview = smsService.previewSms(message.trim());

  const response: ApiResponse = {
    success: true,
    data: preview,
  };

  res.json(response);
});

// SMS şablonlarını getir
export const getSmsTemplates = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const response: ApiResponse = {
    success: true,
    data: {
      templates: SMS_TEMPLATES,
    },
  };

  res.json(response);
});

// Alıcıları belirle (helper function)
async function getRecipients(
  schoolId: string, 
  recipientType: string, 
  recipientId?: string
): Promise<Array<{ phone: string; name: string }>> {
  const recipients: Array<{ phone: string; name: string }> = [];

  switch (recipientType) {
    case 'individual':
      if (!recipientId) {
        throw new ValidationError('Student ID is required for individual SMS');
      }
      
      const studentDoc = await getStudentsRef(schoolId).doc(recipientId).get();
      if (!studentDoc.exists) {
        throw new NotFoundError('Student not found');
      }
      
      const studentData = studentDoc.data()!;
      recipients.push({
        phone: studentData.parentPhone,
        name: studentData.parentName || `${studentData.firstName} ${studentData.lastName} Velisi`,
      });
      break;

    case 'class':
      if (!recipientId) {
        throw new ValidationError('Class name is required for class SMS');
      }
      
      const classStudentsSnapshot = await getStudentsRef(schoolId)
        .where('className', '==', recipientId)
        .get();
      
      classStudentsSnapshot.docs.forEach(doc => {
        const data = doc.data();
        recipients.push({
          phone: data.parentPhone,
          name: data.parentName || `${data.firstName} ${data.lastName} Velisi`,
        });
      });
      break;

    case 'school':
      const allStudentsSnapshot = await getStudentsRef(schoolId).get();
      
      allStudentsSnapshot.docs.forEach(doc => {
        const data = doc.data();
        recipients.push({
          phone: data.parentPhone,
          name: data.parentName || `${data.firstName} ${data.lastName} Velisi`,
        });
      });
      break;

    default:
      throw new ValidationError('Invalid recipient type');
  }

  // Telefon numarası olmayan alıcıları filtrele
  return recipients.filter(r => r.phone && r.phone.trim().length > 0);
}
