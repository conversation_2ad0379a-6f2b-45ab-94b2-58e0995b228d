import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { db, collections } from '../config/firebase';
import { asyncHandler } from '../middleware/errorHandler';
import { ValidationError, AuthenticationError } from '../types';
import type { LoginRequest, LoginResponse } from '../../../shared/types';
import type { ApiResponse } from '../types';

// Kullanıcı girişi
export const login = asyncHandler(async (req: Request, res: Response) => {
  const { email, password }: LoginRequest = req.body;

  // Validation
  if (!email || !password) {
    throw new ValidationError('Email and password are required');
  }

  // Kullanıcıyı email ile bul
  const usersSnapshot = await db
    .collection(collections.users)
    .where('email', '==', email)
    .limit(1)
    .get();

  if (usersSnapshot.empty) {
    throw new AuthenticationError('Invalid email or password');
  }

  const userDoc = usersSnapshot.docs[0];
  const userData = userDoc.data();

  // Şifre kontrolü
  const isPasswordValid = await bcrypt.compare(password, userData.password);
  if (!isPasswordValid) {
    throw new AuthenticationError('Invalid email or password');
  }

  // JWT token oluştur
  const jwtSecret = process.env.JWT_SECRET || 'fallback-secret';
  const token = jwt.sign({ userId: userDoc.id }, jwtSecret, { expiresIn: '7d' });

  // Şifreyi response'dan çıkar
  const { password: _, ...userWithoutPassword } = userData;

  const response: ApiResponse<LoginResponse> = {
    success: true,
    data: {
      user: {
        id: userDoc.id,
        email: userWithoutPassword.email,
        name: userWithoutPassword.name,
        role: userWithoutPassword.role,
        schoolId: userWithoutPassword.schoolId,
        createdAt: userWithoutPassword.createdAt,
        updatedAt: userWithoutPassword.updatedAt,
      },
      token,
    },
    message: 'Login successful',
  };

  res.json(response);
});

// Kullanıcı kaydı (sadece admin tarafından)
export const register = asyncHandler(async (req: Request, res: Response) => {
  const { email, password, name, role, schoolId } = req.body;

  // Validation
  if (!email || !password || !name || !role) {
    throw new ValidationError('Email, password, name, and role are required');
  }

  if (role === 'school_admin' && !schoolId) {
    throw new ValidationError('School ID is required for school admin');
  }

  // Email'in zaten kullanılıp kullanılmadığını kontrol et
  const existingUserSnapshot = await db
    .collection(collections.users)
    .where('email', '==', email)
    .limit(1)
    .get();

  if (!existingUserSnapshot.empty) {
    throw new ValidationError('Email already exists');
  }

  // Şifreyi hash'le
  const hashedPassword = await bcrypt.hash(password, 12);

  // Yeni kullanıcı oluştur
  const newUser = {
    email,
    password: hashedPassword,
    name,
    role,
    ...(schoolId && { schoolId }),
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const userRef = await db.collection(collections.users).add(newUser);

  // Şifreyi response'dan çıkar
  const { password: _, ...userWithoutPassword } = newUser;

  const response: ApiResponse = {
    success: true,
    data: {
      id: userRef.id,
      email: userWithoutPassword.email,
      name: userWithoutPassword.name,
      role: userWithoutPassword.role,
      schoolId: userWithoutPassword.schoolId,
      createdAt: userWithoutPassword.createdAt,
      updatedAt: userWithoutPassword.updatedAt,
    },
    message: 'User created successfully',
  };

  res.status(201).json(response);
});

// Token doğrulama
export const verifyToken = asyncHandler(async (req: Request, res: Response) => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    throw new AuthenticationError('Access token required');
  }

  try {
    const jwtSecret = process.env.JWT_SECRET || 'fallback-secret';
    const decoded = jwt.verify(token, jwtSecret) as { userId: string };

    const userDoc = await db.collection(collections.users).doc(decoded.userId).get();
    
    if (!userDoc.exists) {
      throw new AuthenticationError('User not found');
    }

    const userData = userDoc.data();
    const { password: _, ...userWithoutPassword } = userData!;

    const response: ApiResponse = {
      success: true,
      data: {
        id: userDoc.id,
        email: userWithoutPassword.email,
        name: userWithoutPassword.name,
        role: userWithoutPassword.role,
        schoolId: userWithoutPassword.schoolId,
        createdAt: userWithoutPassword.createdAt,
        updatedAt: userWithoutPassword.updatedAt,
      },
    };

    res.json(response);
  } catch (error) {
    throw new AuthenticationError('Invalid or expired token');
  }
});
