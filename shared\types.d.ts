export interface User {
    id: string;
    email: string;
    name: string;
    role: 'admin' | 'school_admin';
    schoolId?: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface School {
    id: string;
    name: string;
    address: string;
    phone: string;
    email: string;
    adminId: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface Student {
    id: string;
    schoolId: string;
    studentNumber: string;
    firstName: string;
    lastName: string;
    className: string;
    grade: number;
    parentPhone: string;
    parentEmail?: string;
    parentName: string;
    address?: string;
    birthDate?: Date;
    createdAt: Date;
    updatedAt: Date;
}
export interface Class {
    id: string;
    schoolId: string;
    name: string;
    grade: number;
    teacherName?: string;
    studentCount: number;
    createdAt: Date;
    updatedAt: Date;
}
export interface SmsMessage {
    id: string;
    schoolId: string;
    senderId: string;
    recipientType: 'individual' | 'class' | 'school';
    recipientId?: string;
    recipientPhone: string;
    message: string;
    originalMessage?: string;
    status: 'pending' | 'sent' | 'failed';
    sentAt?: Date;
    createdAt: Date;
    updatedAt: Date;
}
export interface ChatMessage {
    id: string;
    userId: string;
    schoolId: string;
    message: string;
    response?: string;
    intent?: string;
    parameters?: Record<string, any>;
    status: 'processing' | 'completed' | 'failed';
    createdAt: Date;
}
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    error?: string;
}
export interface LoginRequest {
    email: string;
    password: string;
}
export interface LoginResponse {
    user: User;
    token: string;
}
export interface SendSmsRequest {
    recipientType: 'individual' | 'class' | 'school';
    recipientId?: string;
    message: string;
    useAiEnhancement?: boolean;
}
export interface AiEnhanceRequest {
    message: string;
    context?: string;
}
export interface AiEnhanceResponse {
    originalMessage: string;
    enhancedMessage: string;
}
export interface ChatRequest {
    message: string;
}
export interface ChatResponse {
    response: string;
    intent?: string;
    parameters?: Record<string, any>;
    actions?: Array<{
        type: string;
        description: string;
        requiresConfirmation: boolean;
    }>;
}
export interface SmsTemplate {
    id: string;
    name: string;
    message: string;
    category: 'attendance' | 'behavior' | 'academic' | 'general';
}
export declare const SMS_TEMPLATES: SmsTemplate[];
//# sourceMappingURL=types.d.ts.map