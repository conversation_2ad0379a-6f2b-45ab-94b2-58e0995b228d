"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const smsController_1 = require("../controllers/smsController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
router.post('/schools/:schoolId/sms/send', auth_1.authenticateToken, auth_1.requireSchoolAccess, smsController_1.sendSms);
router.get('/schools/:schoolId/sms/history', auth_1.authenticateToken, auth_1.requireSchoolAccess, smsController_1.getSmsHistory);
router.get('/schools/:schoolId/sms/stats', auth_1.authenticateToken, auth_1.requireSchoolAccess, smsController_1.getSmsStats);
router.post('/sms/preview', auth_1.authenticateToken, smsController_1.previewSms);
router.get('/sms/templates', auth_1.authenticateToken, smsController_1.getSmsTemplates);
exports.default = router;
//# sourceMappingURL=sms.js.map