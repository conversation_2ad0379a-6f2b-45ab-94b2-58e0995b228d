"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const schoolController_1 = require("../controllers/schoolController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
router.get('/', auth_1.authenticateToken, auth_1.requireAdmin, schoolController_1.getSchools);
router.post('/', auth_1.authenticateToken, auth_1.requireAdmin, schoolController_1.createSchool);
router.get('/:schoolId', auth_1.authenticateToken, auth_1.requireSchoolAccess, schoolController_1.getSchool);
router.put('/:schoolId', auth_1.authenticateToken, auth_1.requireSchoolAccess, schoolController_1.updateSchool);
router.delete('/:schoolId', auth_1.authenticateToken, auth_1.requireAdmin, schoolController_1.deleteSchool);
router.get('/:schoolId/stats', auth_1.authenticateToken, auth_1.requireSchoolAccess, schoolController_1.getSchoolStats);
exports.default = router;
//# sourceMappingURL=schools.js.map