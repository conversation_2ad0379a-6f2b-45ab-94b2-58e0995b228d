import { Response } from 'express';
export declare const sendSms: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const getSmsHistory: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const getSmsStats: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const previewSms: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const getSmsTemplates: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
//# sourceMappingURL=smsController.d.ts.map