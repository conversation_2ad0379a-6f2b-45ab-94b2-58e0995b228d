"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.requireSchoolAccess = exports.requireSchoolAdmin = exports.requireAdmin = exports.authenticateToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const firebase_1 = require("../config/firebase");
const types_1 = require("../types");
const authenticateToken = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1];
        if (!token) {
            throw new types_1.AuthenticationError('Access token required');
        }
        const jwtSecret = process.env.JWT_SECRET || 'fallback-secret';
        const decoded = jsonwebtoken_1.default.verify(token, jwtSecret);
        const userDoc = await firebase_1.db.collection(firebase_1.collections.users).doc(decoded.userId).get();
        if (!userDoc.exists) {
            throw new types_1.AuthenticationError('User not found');
        }
        const userData = userDoc.data();
        req.user = {
            id: decoded.userId,
            email: userData.email,
            role: userData.role,
            schoolId: userData.schoolId,
        };
        next();
    }
    catch (error) {
        if (error instanceof types_1.AuthenticationError) {
            res.status(401).json({
                success: false,
                error: error.message,
            });
            return;
        }
        res.status(401).json({
            success: false,
            error: 'Invalid or expired token',
        });
        return;
    }
};
exports.authenticateToken = authenticateToken;
const requireAdmin = (req, res, next) => {
    if (!req.user || req.user.role !== 'admin') {
        res.status(403).json({
            success: false,
            error: 'Admin access required',
        });
        return;
    }
    next();
};
exports.requireAdmin = requireAdmin;
const requireSchoolAdmin = (req, res, next) => {
    if (!req.user || (req.user.role !== 'admin' && req.user.role !== 'school_admin')) {
        res.status(403).json({
            success: false,
            error: 'School admin access required',
        });
        return;
    }
    next();
};
exports.requireSchoolAdmin = requireSchoolAdmin;
const requireSchoolAccess = (req, res, next) => {
    const schoolId = req.params.schoolId || req.body.schoolId;
    if (!req.user) {
        res.status(401).json({
            success: false,
            error: 'Authentication required',
        });
        return;
    }
    if (req.user.role === 'admin') {
        next();
        return;
    }
    if (req.user.role === 'school_admin' && req.user.schoolId === schoolId) {
        next();
        return;
    }
    res.status(403).json({
        success: false,
        error: 'Access denied to this school',
    });
};
exports.requireSchoolAccess = requireSchoolAccess;
//# sourceMappingURL=auth.js.map