import { Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { db, collections } from '../config/firebase';
import { AuthenticatedRequest, AuthenticationError, AuthorizationError } from '../types';

// JWT token'ı doğrula ve kullanıcı bilgilerini request'e ekle
export const authenticateToken = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      throw new AuthenticationError('Access token required');
    }

    // JWT token'ı doğrula
    const jwtSecret = process.env.JWT_SECRET || 'fallback-secret';
    const decoded = jwt.verify(token, jwtSecret) as { userId: string };
    
    // Kullanıcı bilgilerini veritabanından al
    const userDoc = await db.collection(collections.users).doc(decoded.userId).get();
    
    if (!userDoc.exists) {
      throw new AuthenticationError('User not found');
    }

    const userData = userDoc.data();
    req.user = {
      id: decoded.userId,
      email: userData!.email,
      role: userData!.role,
      schoolId: userData!.schoolId,
    };

    next();
  } catch (error) {
    if (error instanceof AuthenticationError) {
      res.status(401).json({
        success: false,
        error: error.message,
      });
      return;
    }

    res.status(401).json({
      success: false,
      error: 'Invalid or expired token',
    });
  }
};

// Admin yetkisi kontrolü
export const requireAdmin = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  if (!req.user || req.user.role !== 'admin') {
    res.status(403).json({
      success: false,
      error: 'Admin access required',
    });
    return;
  }
  next();
};

// Okul yöneticisi yetkisi kontrolü
export const requireSchoolAdmin = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  if (!req.user || (req.user.role !== 'admin' && req.user.role !== 'school_admin')) {
    res.status(403).json({
      success: false,
      error: 'School admin access required',
    });
    return;
  }
  next();
};

// Okul erişim kontrolü - kullanıcı sadece kendi okuluna erişebilir
export const requireSchoolAccess = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  const schoolId = req.params.schoolId || req.body.schoolId;
  
  if (!req.user) {
    res.status(401).json({
      success: false,
      error: 'Authentication required',
    });
    return;
  }

  // Admin her okula erişebilir
  if (req.user.role === 'admin') {
    next();
    return;
  }

  // School admin sadece kendi okuluna erişebilir
  if (req.user.role === 'school_admin' && req.user.schoolId === schoolId) {
    next();
    return;
  }

  res.status(403).json({
    success: false,
    error: 'Access denied to this school',
  });
};
