"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = __importDefault(require("./auth"));
const ai_1 = __importDefault(require("./ai"));
const sms_1 = __importDefault(require("./sms"));
const schools_1 = __importDefault(require("./schools"));
const users_1 = __importDefault(require("./users"));
const students_1 = __importDefault(require("./students"));
const router = (0, express_1.Router)();
router.use('/auth', auth_1.default);
router.use('/ai', ai_1.default);
router.use('/schools', schools_1.default);
router.use('/users', users_1.default);
router.use('/', students_1.default);
router.use('/', sms_1.default);
router.get('/health', (req, res) => {
    res.json({
        success: true,
        message: 'API is running',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
    });
});
exports.default = router;
//# sourceMappingURL=index.js.map