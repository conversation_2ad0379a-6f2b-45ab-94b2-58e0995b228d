import { Response } from 'express';
import { AuthenticatedRequest, ValidationError, NotFoundError } from '../types';
import { asyncHandler } from '../middleware/errorHandler';
import { getStudentsRef, getClassesRef } from '../config/firebase';
import type { Student } from '../../../shared/types';
import type { ApiResponse } from '../types';

// Öğrencileri getir
export const getStudents = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const schoolId = req.params.schoolId;
  const limit = parseInt(req.query.limit as string) || 50;
  const page = parseInt(req.query.page as string) || 1;
  const search = req.query.search as string;
  const className = req.query.className as string;
  const grade = req.query.grade ? parseInt(req.query.grade as string) : null;

  let query = getStudentsRef(schoolId).orderBy('firstName').limit(limit);

  // Filtreler
  if (className) {
    query = query.where('className', '==', className);
  }
  
  if (grade) {
    query = query.where('grade', '==', grade);
  }

  const snapshot = await query.get();
  
  let students: Student[] = snapshot.docs.map(doc => {
    const data = doc.data();
    return {
      id: doc.id,
      ...data,
      createdAt: data.createdAt.toDate(),
      updatedAt: data.updatedAt.toDate(),
      birthDate: data.birthDate?.toDate() || null,
    } as Student;
  });

  // Arama filtresi (client-side - daha iyi performans için server-side yapılabilir)
  if (search) {
    const searchLower = search.toLowerCase();
    students = students.filter(student => 
      student.firstName.toLowerCase().includes(searchLower) ||
      student.lastName.toLowerCase().includes(searchLower) ||
      student.studentNumber.toLowerCase().includes(searchLower) ||
      student.parentName.toLowerCase().includes(searchLower)
    );
  }

  const response: ApiResponse = {
    success: true,
    data: {
      students,
      pagination: {
        page,
        limit,
        total: students.length,
        hasMore: students.length === limit,
      },
    },
  };

  res.json(response);
});

// Tek öğrenci getir
export const getStudent = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { schoolId, studentId } = req.params;
  
  const studentDoc = await getStudentsRef(schoolId).doc(studentId).get();
  
  if (!studentDoc.exists) {
    throw new NotFoundError('Student not found');
  }

  const studentData = studentDoc.data()!;
  
  const response: ApiResponse = {
    success: true,
    data: {
      id: studentDoc.id,
      ...studentData,
      createdAt: studentData.createdAt.toDate(),
      updatedAt: studentData.updatedAt.toDate(),
      birthDate: studentData.birthDate?.toDate() || null,
    },
  };

  res.json(response);
});

// Yeni öğrenci oluştur
export const createStudent = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const schoolId = req.params.schoolId;
  const { 
    studentNumber, 
    firstName, 
    lastName, 
    className, 
    grade, 
    parentPhone, 
    parentEmail, 
    parentName,
    address,
    birthDate
  } = req.body;

  // Validation
  if (!studentNumber || !firstName || !lastName || !className || !grade || !parentPhone || !parentName) {
    throw new ValidationError('Required fields: studentNumber, firstName, lastName, className, grade, parentPhone, parentName');
  }

  // Aynı öğrenci numarası var mı kontrol et
  const existingStudentSnapshot = await getStudentsRef(schoolId)
    .where('studentNumber', '==', studentNumber)
    .limit(1)
    .get();

  if (!existingStudentSnapshot.empty) {
    throw new ValidationError('Student number already exists');
  }

  // Yeni öğrenci oluştur
  const newStudent = {
    schoolId,
    studentNumber,
    firstName,
    lastName,
    className,
    grade: parseInt(grade),
    parentPhone,
    parentEmail: parentEmail || null,
    parentName,
    address: address || null,
    birthDate: birthDate ? new Date(birthDate) : null,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const studentRef = await getStudentsRef(schoolId).add(newStudent);

  // Sınıf öğrenci sayısını güncelle
  await updateClassStudentCount(schoolId, className);

  const response: ApiResponse = {
    success: true,
    data: {
      id: studentRef.id,
      ...newStudent,
    },
    message: 'Student created successfully',
  };

  res.status(201).json(response);
});

// Öğrenci güncelle
export const updateStudent = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { schoolId, studentId } = req.params;
  const updateData = req.body;

  const studentDoc = await getStudentsRef(schoolId).doc(studentId).get();
  
  if (!studentDoc.exists) {
    throw new NotFoundError('Student not found');
  }

  const currentData = studentDoc.data()!;
  const oldClassName = currentData.className;

  // Öğrenci numarası değişiyorsa, aynı numarada başka öğrenci var mı kontrol et
  if (updateData.studentNumber && updateData.studentNumber !== currentData.studentNumber) {
    const existingStudentSnapshot = await getStudentsRef(schoolId)
      .where('studentNumber', '==', updateData.studentNumber)
      .limit(1)
      .get();

    if (!existingStudentSnapshot.empty) {
      throw new ValidationError('Student number already exists');
    }
  }

  // Güncelleme verilerini hazırla
  const finalUpdateData: any = {
    ...updateData,
    updatedAt: new Date(),
  };

  if (updateData.birthDate) {
    finalUpdateData.birthDate = new Date(updateData.birthDate);
  }

  if (updateData.grade) {
    finalUpdateData.grade = parseInt(updateData.grade);
  }

  await studentDoc.ref.update(finalUpdateData);

  // Sınıf değiştiyse, eski ve yeni sınıfların öğrenci sayısını güncelle
  if (updateData.className && updateData.className !== oldClassName) {
    await Promise.all([
      updateClassStudentCount(schoolId, oldClassName),
      updateClassStudentCount(schoolId, updateData.className),
    ]);
  }

  const response: ApiResponse = {
    success: true,
    message: 'Student updated successfully',
  };

  res.json(response);
});

// Öğrenci sil
export const deleteStudent = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { schoolId, studentId } = req.params;

  const studentDoc = await getStudentsRef(schoolId).doc(studentId).get();
  
  if (!studentDoc.exists) {
    throw new NotFoundError('Student not found');
  }

  const studentData = studentDoc.data()!;
  const className = studentData.className;

  await studentDoc.ref.delete();

  // Sınıf öğrenci sayısını güncelle
  await updateClassStudentCount(schoolId, className);

  const response: ApiResponse = {
    success: true,
    message: 'Student deleted successfully',
  };

  res.json(response);
});

// Toplu öğrenci import
export const importStudents = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const schoolId = req.params.schoolId;
  const { students } = req.body;

  if (!Array.isArray(students) || students.length === 0) {
    throw new ValidationError('Students array is required');
  }

  const results = {
    successful: 0,
    failed: 0,
    errors: [] as string[],
  };

  // Batch işlemi için
  const batch = getStudentsRef(schoolId).firestore.batch();
  const classUpdates = new Set<string>();

  for (let i = 0; i < students.length; i++) {
    const student = students[i];
    
    try {
      // Validation
      if (!student.studentNumber || !student.firstName || !student.lastName || 
          !student.className || !student.grade || !student.parentPhone || !student.parentName) {
        throw new Error(`Row ${i + 1}: Missing required fields`);
      }

      // Öğrenci numarası kontrolü
      const existingStudentSnapshot = await getStudentsRef(schoolId)
        .where('studentNumber', '==', student.studentNumber)
        .limit(1)
        .get();

      if (!existingStudentSnapshot.empty) {
        throw new Error(`Row ${i + 1}: Student number ${student.studentNumber} already exists`);
      }

      const newStudent = {
        schoolId,
        studentNumber: student.studentNumber,
        firstName: student.firstName,
        lastName: student.lastName,
        className: student.className,
        grade: parseInt(student.grade),
        parentPhone: student.parentPhone,
        parentEmail: student.parentEmail || null,
        parentName: student.parentName,
        address: student.address || null,
        birthDate: student.birthDate ? new Date(student.birthDate) : null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const studentRef = getStudentsRef(schoolId).doc();
      batch.set(studentRef, newStudent);
      
      classUpdates.add(student.className);
      results.successful++;
    } catch (error: any) {
      results.failed++;
      results.errors.push(error.message);
    }
  }

  // Batch commit
  if (results.successful > 0) {
    await batch.commit();

    // Sınıf öğrenci sayılarını güncelle
    for (const className of classUpdates) {
      await updateClassStudentCount(schoolId, className);
    }
  }

  const response: ApiResponse = {
    success: true,
    data: results,
    message: `Import completed: ${results.successful} successful, ${results.failed} failed`,
  };

  res.json(response);
});

// Sınıf öğrenci sayısını güncelle (helper function)
async function updateClassStudentCount(schoolId: string, className: string) {
  try {
    const studentsSnapshot = await getStudentsRef(schoolId)
      .where('className', '==', className)
      .count()
      .get();

    const studentCount = studentsSnapshot.data().count;

    // Sınıf var mı kontrol et
    const classSnapshot = await getClassesRef(schoolId)
      .where('name', '==', className)
      .limit(1)
      .get();

    if (!classSnapshot.empty) {
      const classDoc = classSnapshot.docs[0];
      await classDoc.ref.update({
        studentCount,
        updatedAt: new Date(),
      });
    } else {
      // Sınıf yoksa oluştur
      await getClassesRef(schoolId).add({
        schoolId,
        name: className,
        grade: parseInt(className.split('-')[0]) || 0,
        studentCount,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }
  } catch (error) {
    console.error('Error updating class student count:', error);
  }
}
