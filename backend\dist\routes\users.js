"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const userController_1 = require("../controllers/userController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
router.get('/', auth_1.authenticateToken, auth_1.requireAdmin, userController_1.getUsers);
router.get('/stats', auth_1.authenticateToken, auth_1.requireAdmin, userController_1.getUserStats);
router.get('/:userId', auth_1.authenticateToken, userController_1.getUser);
router.put('/:userId', auth_1.authenticateToken, userController_1.updateUser);
router.delete('/:userId', auth_1.authenticateToken, auth_1.requireAdmin, userController_1.deleteUser);
router.put('/:userId/password', auth_1.authenticateToken, userController_1.changePassword);
router.get('/schools/:schoolId/users', auth_1.authenticateToken, auth_1.requireSchoolAccess, userController_1.getUsersBySchool);
exports.default = router;
//# sourceMappingURL=users.js.map