import { Response } from 'express';
export declare const getUsers: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const getUser: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const updateUser: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const deleteUser: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const changePassword: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const getUsersBySchool: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const getUserStats: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
//# sourceMappingURL=userController.d.ts.map