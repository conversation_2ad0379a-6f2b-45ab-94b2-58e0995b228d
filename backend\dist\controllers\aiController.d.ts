import { Response } from 'express';
export declare const enhanceMessage: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const sendChatMessage: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const getChatHistory: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const deleteChatMessage: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const getChatStats: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
//# sourceMappingURL=aiController.d.ts.map