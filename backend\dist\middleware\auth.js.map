{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;;;;AACA,gEAA+B;AAC/B,iDAAqD;AACrD,oCAAyF;AAGlF,MAAM,iBAAiB,GAAG,KAAK,EACpC,GAAyB,EACzB,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAC7C,MAAM,KAAK,GAAG,UAAU,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAErD,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,2BAAmB,CAAC,uBAAuB,CAAC,CAAC;QACzD,CAAC;QAGD,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,iBAAiB,CAAC;QAC9D,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,SAAS,CAAuB,CAAC;QAGnE,MAAM,OAAO,GAAG,MAAM,aAAE,CAAC,UAAU,CAAC,sBAAW,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;QAEjF,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACpB,MAAM,IAAI,2BAAmB,CAAC,gBAAgB,CAAC,CAAC;QAClD,CAAC;QAED,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAChC,GAAG,CAAC,IAAI,GAAG;YACT,EAAE,EAAE,OAAO,CAAC,MAAM;YAClB,KAAK,EAAE,QAAS,CAAC,KAAK;YACtB,IAAI,EAAE,QAAS,CAAC,IAAI;YACpB,QAAQ,EAAE,QAAS,CAAC,QAAQ;SAC7B,CAAC;QAEF,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,2BAAmB,EAAE,CAAC;YACzC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,0BAA0B;SAClC,CAAC,CAAC;QACH,OAAO;IACT,CAAC;AACH,CAAC,CAAC;AAhDW,QAAA,iBAAiB,qBAgD5B;AAGK,MAAM,YAAY,GAAG,CAC1B,GAAyB,EACzB,GAAa,EACb,IAAkB,EACZ,EAAE;IACR,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QAC3C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,uBAAuB;SAC/B,CAAC,CAAC;QACH,OAAO;IACT,CAAC;IACD,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAbW,QAAA,YAAY,gBAavB;AAGK,MAAM,kBAAkB,GAAG,CAChC,GAAyB,EACzB,GAAa,EACb,IAAkB,EACZ,EAAE;IACR,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,cAAc,CAAC,EAAE,CAAC;QACjF,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,8BAA8B;SACtC,CAAC,CAAC;QACH,OAAO;IACT,CAAC;IACD,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAbW,QAAA,kBAAkB,sBAa7B;AAGK,MAAM,mBAAmB,GAAG,CACjC,GAAyB,EACzB,GAAa,EACb,IAAkB,EACZ,EAAE;IACR,MAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;IAE1D,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,yBAAyB;SACjC,CAAC,CAAC;QACH,OAAO;IACT,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QAC9B,IAAI,EAAE,CAAC;QACP,OAAO;IACT,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,cAAc,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;QACvE,IAAI,EAAE,CAAC;QACP,OAAO;IACT,CAAC;IAED,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;QACnB,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,8BAA8B;KACtC,CAAC,CAAC;AACL,CAAC,CAAC;AA/BW,QAAA,mBAAmB,uBA+B9B"}