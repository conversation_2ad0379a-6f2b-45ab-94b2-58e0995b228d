{"version": 3, "file": "aiService.js", "sourceRoot": "", "sources": ["../../src/services/aiService.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAG1B,MAAM,SAAS;IAIb;QAFQ,kBAAa,GAAG,kDAAkD,CAAC;QAGzE,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,EAAE,CAAC;QACrD,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,OAAO,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,cAAc,CAAC,eAAuB,EAAE,OAAgB;QAC5D,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;gBACvB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;YACnD,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;YAEjE,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,IAAI,CAC/B,GAAG,IAAI,CAAC,aAAa,0CAA0C,IAAI,CAAC,YAAY,EAAE,EAClF;gBACE,QAAQ,EAAE,CAAC;wBACT,KAAK,EAAE,CAAC;gCACN,IAAI,EAAE,MAAM;6BACb,CAAC;qBACH,CAAC;gBACF,gBAAgB,EAAE;oBAChB,WAAW,EAAE,GAAG;oBAChB,IAAI,EAAE,EAAE;oBACR,IAAI,EAAE,IAAI;oBACV,eAAe,EAAE,IAAI;iBACtB;aACF,EACD;gBACE,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;aACF,CACF,CAAC;YAEF,MAAM,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YAEjF,OAAO;gBACL,eAAe;gBACf,eAAe;gBACf,OAAO,EAAE,IAAI;aACd,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC9C,OAAO;gBACL,eAAe;gBACf,eAAe,EAAE,eAAe;gBAChC,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,uBAAuB;aAChD,CAAC;QACJ,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,kBAAkB,CAAC,OAAe,EAAE,OAAa;QACrD,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;gBACvB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;YACnD,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAEtD,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,IAAI,CAC/B,GAAG,IAAI,CAAC,aAAa,0CAA0C,IAAI,CAAC,YAAY,EAAE,EAClF;gBACE,QAAQ,EAAE,CAAC;wBACT,KAAK,EAAE,CAAC;gCACN,IAAI,EAAE,MAAM;6BACb,CAAC;qBACH,CAAC;gBACF,gBAAgB,EAAE;oBAChB,WAAW,EAAE,GAAG;oBAChB,IAAI,EAAE,EAAE;oBACR,IAAI,EAAE,GAAG;oBACT,eAAe,EAAE,IAAI;iBACtB;aACF,EACD;gBACE,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;aACF,CACF,CAAC;YAEF,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YAG5E,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAE1D,OAAO;gBACL,QAAQ,EAAE,cAAc,CAAC,QAAQ;gBACjC,MAAM,EAAE,cAAc,CAAC,MAAM;gBAC7B,UAAU,EAAE,cAAc,CAAC,UAAU;gBACrC,OAAO,EAAE,cAAc,CAAC,OAAO;gBAC/B,OAAO,EAAE,IAAI;aACd,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;YACvC,OAAO;gBACL,QAAQ,EAAE,8EAA8E;gBACxF,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,2BAA2B;aACpD,CAAC;QACJ,CAAC;IACH,CAAC;IAGO,kBAAkB,CAAC,OAAe,EAAE,OAAgB;QAC1D,OAAO;;;;EAIT,OAAO,CAAC,CAAC,CAAC,WAAW,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE;;mBAElB,OAAO;;;;;;;;;;;;CAYzB,CAAC;IACA,CAAC;IAGO,eAAe,CAAC,OAAe,EAAE,OAAa;QACpD,OAAO;;;;;;;;;;qBAUU,OAAO;;EAE1B,OAAO,CAAC,CAAC,CAAC,mBAAmB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;CAqB5D,CAAC;IACA,CAAC;IAGO,iBAAiB,CAAC,UAAkB;QAC1C,IAAI,CAAC;YAEH,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAClD,IAAI,SAAS,EAAE,CAAC;gBACd,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC;YAGD,OAAO;gBACL,QAAQ,EAAE,UAAU;gBACpB,MAAM,EAAE,kBAAkB;gBAC1B,UAAU,EAAE,EAAE;gBACd,OAAO,EAAE,EAAE;aACZ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,QAAQ,EAAE,UAAU;gBACpB,MAAM,EAAE,kBAAkB;gBAC1B,UAAU,EAAE,EAAE;gBACd,OAAO,EAAE,EAAE;aACZ,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAEY,QAAA,SAAS,GAAG,IAAI,SAAS,EAAE,CAAC;AACzC,kBAAe,iBAAS,CAAC"}