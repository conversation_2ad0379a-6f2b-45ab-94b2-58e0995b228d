"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.aiService = void 0;
const axios_1 = __importDefault(require("axios"));
class AiService {
    constructor() {
        this.geminiBaseUrl = 'https://generativelanguage.googleapis.com/v1beta';
        this.geminiApiKey = process.env.GEMINI_API_KEY || '';
        if (!this.geminiApiKey) {
            console.warn('GEMINI_API_KEY not found in environment variables');
        }
    }
    async enhanceMessage(originalMessage, context) {
        try {
            if (!this.geminiApiKey) {
                throw new Error('Gemini API key not configured');
            }
            const prompt = this.buildEnhancePrompt(originalMessage, context);
            const response = await axios_1.default.post(`${this.geminiBaseUrl}/models/gemini-pro:generateContent?key=${this.geminiApiKey}`, {
                contents: [{
                        parts: [{
                                text: prompt
                            }]
                    }],
                generationConfig: {
                    temperature: 0.7,
                    topK: 40,
                    topP: 0.95,
                    maxOutputTokens: 1024,
                }
            }, {
                headers: {
                    'Content-Type': 'application/json',
                },
            });
            const enhancedMessage = response.data.candidates[0].content.parts[0].text.trim();
            return {
                originalMessage,
                enhancedMessage,
                success: true,
            };
        }
        catch (error) {
            console.error('AI Enhancement Error:', error);
            return {
                originalMessage,
                enhancedMessage: originalMessage,
                success: false,
                error: error.message || 'AI enhancement failed',
            };
        }
    }
    async processChatMessage(message, context) {
        try {
            if (!this.geminiApiKey) {
                throw new Error('Gemini API key not configured');
            }
            const prompt = this.buildChatPrompt(message, context);
            const response = await axios_1.default.post(`${this.geminiBaseUrl}/models/gemini-pro:generateContent?key=${this.geminiApiKey}`, {
                contents: [{
                        parts: [{
                                text: prompt
                            }]
                    }],
                generationConfig: {
                    temperature: 0.3,
                    topK: 20,
                    topP: 0.8,
                    maxOutputTokens: 2048,
                }
            }, {
                headers: {
                    'Content-Type': 'application/json',
                },
            });
            const aiResponse = response.data.candidates[0].content.parts[0].text.trim();
            const parsedResponse = this.parseChatResponse(aiResponse);
            return {
                response: parsedResponse.response,
                intent: parsedResponse.intent,
                parameters: parsedResponse.parameters,
                actions: parsedResponse.actions,
                success: true,
            };
        }
        catch (error) {
            console.error('AI Chat Error:', error);
            return {
                response: 'Üzgünüm, şu anda isteğinizi işleyemiyorum. Lütfen daha sonra tekrar deneyin.',
                success: false,
                error: error.message || 'AI chat processing failed',
            };
        }
    }
    buildEnhancePrompt(message, context) {
        return `
Sen bir okul yönetim sisteminde çalışan profesyonel mesaj iyileştirme asistanısın. 
Görevin, kısa ve gayri resmi notları, nazik, profesyonel ve resmi okul iletişimine uygun mesajlara dönüştürmek.

${context ? `Bağlam: ${context}` : ''}

Orijinal mesaj: "${message}"

Lütfen bu mesajı aşağıdaki kurallara göre iyileştir:
1. Nazik ve saygılı bir ton kullan
2. Resmi okul iletişimine uygun dil kullan
3. "Sayın Veli" ile başla
4. "Saygılarımızla, Okul Yönetimi" ile bitir
5. Açık ve anlaşılır ol
6. Türkçe dilbilgisi kurallarına uy
7. Mesajı gereksiz uzatma, öz ve net ol

Sadece iyileştirilmiş mesajı döndür, başka açıklama yapma.
`;
    }
    buildChatPrompt(message, context) {
        return `
Sen bir okul yönetim sisteminin yapay zeka asistanısın. Kullanıcıların doğal dilde verdiği komutları anlayıp, sistem fonksiyonlarını tetikleyebilirsin.

Mevcut sistem fonksiyonları:
- Öğrenci listesi görüntüleme
- Sınıf bazlı öğrenci filtreleme
- SMS gönderimi (bireysel, sınıf, tüm okul)
- Devamsızlık sorgulama
- Veli iletişim bilgileri görüntüleme

Kullanıcı mesajı: "${message}"

${context ? `Sistem bağlamı: ${JSON.stringify(context)}` : ''}

Lütfen bu mesajı analiz et ve aşağıdaki JSON formatında yanıt ver:

{
  "response": "Kullanıcıya verilecek yanıt",
  "intent": "tespit_edilen_niyet",
  "parameters": {
    "parametre_adı": "değer"
  },
  "actions": [
    {
      "type": "fonksiyon_adı",
      "description": "yapılacak_işlem_açıklaması",
      "requiresConfirmation": true/false
    }
  ]
}

Eğer komut belirsizse veya daha fazla bilgi gerekiyorsa, kullanıcıdan açıklama iste.
Türkçe yanıt ver ve nazik ol.
`;
    }
    parseChatResponse(aiResponse) {
        try {
            const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                return JSON.parse(jsonMatch[0]);
            }
            return {
                response: aiResponse,
                intent: 'general_response',
                parameters: {},
                actions: [],
            };
        }
        catch (error) {
            return {
                response: aiResponse,
                intent: 'general_response',
                parameters: {},
                actions: [],
            };
        }
    }
}
exports.aiService = new AiService();
exports.default = exports.aiService;
//# sourceMappingURL=aiService.js.map