"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.importStudents = exports.deleteStudent = exports.updateStudent = exports.createStudent = exports.getStudent = exports.getStudents = void 0;
const types_1 = require("../types");
const errorHandler_1 = require("../middleware/errorHandler");
const firebase_1 = require("../config/firebase");
exports.getStudents = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const schoolId = req.params.schoolId;
    const limit = parseInt(req.query.limit) || 50;
    const page = parseInt(req.query.page) || 1;
    const search = req.query.search;
    const className = req.query.className;
    const grade = req.query.grade ? parseInt(req.query.grade) : null;
    let query = (0, firebase_1.getStudentsRef)(schoolId).orderBy('firstName').limit(limit);
    if (className) {
        query = query.where('className', '==', className);
    }
    if (grade) {
        query = query.where('grade', '==', grade);
    }
    const snapshot = await query.get();
    let students = snapshot.docs.map(doc => {
        const data = doc.data();
        return {
            id: doc.id,
            ...data,
            createdAt: data.createdAt.toDate(),
            updatedAt: data.updatedAt.toDate(),
            birthDate: data.birthDate?.toDate() || null,
        };
    });
    if (search) {
        const searchLower = search.toLowerCase();
        students = students.filter(student => student.firstName.toLowerCase().includes(searchLower) ||
            student.lastName.toLowerCase().includes(searchLower) ||
            student.studentNumber.toLowerCase().includes(searchLower) ||
            student.parentName.toLowerCase().includes(searchLower));
    }
    const response = {
        success: true,
        data: {
            students,
            pagination: {
                page,
                limit,
                total: students.length,
                hasMore: students.length === limit,
            },
        },
    };
    res.json(response);
});
exports.getStudent = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { schoolId, studentId } = req.params;
    const studentDoc = await (0, firebase_1.getStudentsRef)(schoolId).doc(studentId).get();
    if (!studentDoc.exists) {
        throw new types_1.NotFoundError('Student not found');
    }
    const studentData = studentDoc.data();
    const response = {
        success: true,
        data: {
            id: studentDoc.id,
            ...studentData,
            createdAt: studentData.createdAt.toDate(),
            updatedAt: studentData.updatedAt.toDate(),
            birthDate: studentData.birthDate?.toDate() || null,
        },
    };
    res.json(response);
});
exports.createStudent = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const schoolId = req.params.schoolId;
    const { studentNumber, firstName, lastName, className, grade, parentPhone, parentEmail, parentName, address, birthDate } = req.body;
    if (!studentNumber || !firstName || !lastName || !className || !grade || !parentPhone || !parentName) {
        throw new types_1.ValidationError('Required fields: studentNumber, firstName, lastName, className, grade, parentPhone, parentName');
    }
    const existingStudentSnapshot = await (0, firebase_1.getStudentsRef)(schoolId)
        .where('studentNumber', '==', studentNumber)
        .limit(1)
        .get();
    if (!existingStudentSnapshot.empty) {
        throw new types_1.ValidationError('Student number already exists');
    }
    const newStudent = {
        schoolId,
        studentNumber,
        firstName,
        lastName,
        className,
        grade: parseInt(grade),
        parentPhone,
        parentEmail: parentEmail || null,
        parentName,
        address: address || null,
        birthDate: birthDate ? new Date(birthDate) : null,
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    const studentRef = await (0, firebase_1.getStudentsRef)(schoolId).add(newStudent);
    await updateClassStudentCount(schoolId, className);
    const response = {
        success: true,
        data: {
            id: studentRef.id,
            ...newStudent,
        },
        message: 'Student created successfully',
    };
    res.status(201).json(response);
});
exports.updateStudent = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { schoolId, studentId } = req.params;
    const updateData = req.body;
    const studentDoc = await (0, firebase_1.getStudentsRef)(schoolId).doc(studentId).get();
    if (!studentDoc.exists) {
        throw new types_1.NotFoundError('Student not found');
    }
    const currentData = studentDoc.data();
    const oldClassName = currentData.className;
    if (updateData.studentNumber && updateData.studentNumber !== currentData.studentNumber) {
        const existingStudentSnapshot = await (0, firebase_1.getStudentsRef)(schoolId)
            .where('studentNumber', '==', updateData.studentNumber)
            .limit(1)
            .get();
        if (!existingStudentSnapshot.empty) {
            throw new types_1.ValidationError('Student number already exists');
        }
    }
    const finalUpdateData = {
        ...updateData,
        updatedAt: new Date(),
    };
    if (updateData.birthDate) {
        finalUpdateData.birthDate = new Date(updateData.birthDate);
    }
    if (updateData.grade) {
        finalUpdateData.grade = parseInt(updateData.grade);
    }
    await studentDoc.ref.update(finalUpdateData);
    if (updateData.className && updateData.className !== oldClassName) {
        await Promise.all([
            updateClassStudentCount(schoolId, oldClassName),
            updateClassStudentCount(schoolId, updateData.className),
        ]);
    }
    const response = {
        success: true,
        message: 'Student updated successfully',
    };
    res.json(response);
});
exports.deleteStudent = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { schoolId, studentId } = req.params;
    const studentDoc = await (0, firebase_1.getStudentsRef)(schoolId).doc(studentId).get();
    if (!studentDoc.exists) {
        throw new types_1.NotFoundError('Student not found');
    }
    const studentData = studentDoc.data();
    const className = studentData.className;
    await studentDoc.ref.delete();
    await updateClassStudentCount(schoolId, className);
    const response = {
        success: true,
        message: 'Student deleted successfully',
    };
    res.json(response);
});
exports.importStudents = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const schoolId = req.params.schoolId;
    const { students } = req.body;
    if (!Array.isArray(students) || students.length === 0) {
        throw new types_1.ValidationError('Students array is required');
    }
    const results = {
        successful: 0,
        failed: 0,
        errors: [],
    };
    const batch = (0, firebase_1.getStudentsRef)(schoolId).firestore.batch();
    const classUpdates = new Set();
    for (let i = 0; i < students.length; i++) {
        const student = students[i];
        try {
            if (!student.studentNumber || !student.firstName || !student.lastName ||
                !student.className || !student.grade || !student.parentPhone || !student.parentName) {
                throw new Error(`Row ${i + 1}: Missing required fields`);
            }
            const existingStudentSnapshot = await (0, firebase_1.getStudentsRef)(schoolId)
                .where('studentNumber', '==', student.studentNumber)
                .limit(1)
                .get();
            if (!existingStudentSnapshot.empty) {
                throw new Error(`Row ${i + 1}: Student number ${student.studentNumber} already exists`);
            }
            const newStudent = {
                schoolId,
                studentNumber: student.studentNumber,
                firstName: student.firstName,
                lastName: student.lastName,
                className: student.className,
                grade: parseInt(student.grade),
                parentPhone: student.parentPhone,
                parentEmail: student.parentEmail || null,
                parentName: student.parentName,
                address: student.address || null,
                birthDate: student.birthDate ? new Date(student.birthDate) : null,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            const studentRef = (0, firebase_1.getStudentsRef)(schoolId).doc();
            batch.set(studentRef, newStudent);
            classUpdates.add(student.className);
            results.successful++;
        }
        catch (error) {
            results.failed++;
            results.errors.push(error.message);
        }
    }
    if (results.successful > 0) {
        await batch.commit();
        for (const className of classUpdates) {
            await updateClassStudentCount(schoolId, className);
        }
    }
    const response = {
        success: true,
        data: results,
        message: `Import completed: ${results.successful} successful, ${results.failed} failed`,
    };
    res.json(response);
});
async function updateClassStudentCount(schoolId, className) {
    try {
        const studentsSnapshot = await (0, firebase_1.getStudentsRef)(schoolId)
            .where('className', '==', className)
            .count()
            .get();
        const studentCount = studentsSnapshot.data().count;
        const classSnapshot = await (0, firebase_1.getClassesRef)(schoolId)
            .where('name', '==', className)
            .limit(1)
            .get();
        if (!classSnapshot.empty) {
            const classDoc = classSnapshot.docs[0];
            await classDoc.ref.update({
                studentCount,
                updatedAt: new Date(),
            });
        }
        else {
            await (0, firebase_1.getClassesRef)(schoolId).add({
                schoolId,
                name: className,
                grade: parseInt(className.split('-')[0]) || 0,
                studentCount,
                createdAt: new Date(),
                updatedAt: new Date(),
            });
        }
    }
    catch (error) {
        console.error('Error updating class student count:', error);
    }
}
//# sourceMappingURL=studentController.js.map