"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.smsService = void 0;
const axios_1 = __importDefault(require("axios"));
class SmsService {
    constructor() {
        this.netgsmBaseUrl = 'https://api.netgsm.com.tr/sms/send/get';
        this.netgsmUsername = process.env.NETGSM_USERNAME || '';
        this.netgsmPassword = process.env.NETGSM_PASSWORD || '';
        this.netgsmHeader = process.env.NETGSM_HEADER || '';
        if (!this.netgsmUsername || !this.netgsmPassword) {
            console.warn('NetGSM credentials not found in environment variables');
        }
    }
    async sendSms(phone, message) {
        try {
            if (!this.netgsmUsername || !this.netgsmPassword) {
                throw new Error('NetGSM credentials not configured');
            }
            const cleanPhone = this.cleanPhoneNumber(phone);
            if (!this.isValidPhoneNumber(cleanPhone)) {
                throw new Error('Invalid phone number format');
            }
            const params = new URLSearchParams({
                usercode: this.netgsmUsername,
                password: this.netgsmPassword,
                gsmno: cleanPhone,
                message: message,
                msgheader: this.netgsmHeader,
                filter: '0',
                startdate: '',
                stopdate: '',
            });
            const response = await axios_1.default.get(`${this.netgsmBaseUrl}?${params.toString()}`, {
                timeout: 10000,
            });
            const result = this.parseNetgsmResponse(response.data);
            if (result.success) {
                console.log(`SMS sent successfully to ${cleanPhone}: ${result.messageId}`);
            }
            else {
                console.error(`SMS failed to ${cleanPhone}: ${result.error}`);
            }
            return result;
        }
        catch (error) {
            console.error('SMS sending error:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred',
            };
        }
    }
    async sendBulkSms(phones, message) {
        const results = [];
        for (const phone of phones) {
            const result = await this.sendSms(phone, message);
            results.push(result);
            await this.delay(100);
        }
        return results;
    }
    parseNetgsmResponse(responseData) {
        const response = responseData.trim();
        if (response.startsWith('00 ')) {
            return {
                success: true,
                messageId: response.substring(3),
            };
        }
        const errorMessages = {
            '20': 'Mesaj metninde hata var',
            '30': 'Geçersiz kullanıcı adı, şifre veya kullanıcınız aktif değil',
            '40': 'Mesaj başlığınız (header) sistemde tanımlı değil',
            '70': 'Hatalı sorgulama. Gönderdiğiniz parametrelerden birisi hatalı veya zorunlu alanlardan birisi eksik',
        };
        return {
            success: false,
            error: errorMessages[response] || `Unknown error: ${response}`,
        };
    }
    cleanPhoneNumber(phone) {
        let cleaned = phone.replace(/\D/g, '');
        if (cleaned.startsWith('90')) {
            cleaned = cleaned.substring(2);
        }
        else if (cleaned.startsWith('0')) {
            cleaned = cleaned.substring(1);
        }
        return cleaned;
    }
    isValidPhoneNumber(phone) {
        return /^5\d{9}$/.test(phone);
    }
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    calculateSmsLength(message) {
        const hasUnicodeChars = /[çğıöşüÇĞIİÖŞÜ]/.test(message);
        const encoding = hasUnicodeChars ? 'UCS2' : 'GSM7';
        const maxLength = encoding === 'UCS2' ? 70 : 160;
        const maxConcatLength = encoding === 'UCS2' ? 67 : 153;
        const length = message.length;
        let smsCount = 1;
        let remainingChars = maxLength - length;
        if (length > maxLength) {
            smsCount = Math.ceil(length / maxConcatLength);
            const usedInLastSms = length % maxConcatLength;
            remainingChars = usedInLastSms === 0 ? 0 : maxConcatLength - usedInLastSms;
        }
        return {
            length,
            smsCount,
            encoding,
            remainingChars: Math.max(0, remainingChars),
        };
    }
    previewSms(message) {
        const stats = this.calculateSmsLength(message);
        const estimatedCost = stats.smsCount * 0.05;
        return {
            message,
            stats,
            estimatedCost,
        };
    }
}
exports.smsService = new SmsService();
exports.default = exports.smsService;
//# sourceMappingURL=smsService.js.map