import { Response } from 'express';
import { AuthenticatedRequest, ValidationError } from '../types';
import { asyncHandler } from '../middleware/errorHandler';
import { aiService } from '../services/aiService';
import { db, getChatMessagesRef } from '../config/firebase';
import type { AiEnhanceRequest, AiEnhanceResponse, ChatRequest, ChatResponse } from '../../../shared/types';
import type { ApiResponse } from '../types';

// Mesaj iyileştirme (Sihirli Kalem)
export const enhanceMessage = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { message, context }: AiEnhanceRequest = req.body;

  if (!message || message.trim().length === 0) {
    throw new ValidationError('Message is required');
  }

  if (message.length > 500) {
    throw new ValidationError('Message is too long (max 500 characters)');
  }

  const result = await aiService.enhanceMessage(message.trim(), context);

  const response: ApiResponse<AiEnhanceResponse> = {
    success: result.success,
    data: result.success ? {
      originalMessage: result.originalMessage,
      enhancedMessage: result.enhancedMessage,
    } : undefined,
    message: result.success ? 'Message enhanced successfully' : 'Enhancement failed',
    error: result.error,
  };

  res.json(response);
});

// Chat mesajı gönderme
export const sendChatMessage = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { message }: ChatRequest = req.body;
  const schoolId = req.params.schoolId;
  const userId = req.user!.id;

  if (!message || message.trim().length === 0) {
    throw new ValidationError('Message is required');
  }

  if (message.length > 1000) {
    throw new ValidationError('Message is too long (max 1000 characters)');
  }

  // Chat mesajını veritabanına kaydet
  const chatRef = getChatMessagesRef(schoolId);
  const chatDoc = await chatRef.add({
    userId,
    schoolId,
    message: message.trim(),
    status: 'processing',
    createdAt: new Date(),
  });

  try {
    // AI ile mesajı işle
    const context = {
      userId,
      schoolId,
      userRole: req.user!.role,
    };

    const aiResult = await aiService.processChatMessage(message.trim(), context);

    // Sonucu veritabanında güncelle
    await chatDoc.update({
      response: aiResult.response,
      intent: aiResult.intent,
      parameters: aiResult.parameters,
      status: aiResult.success ? 'completed' : 'failed',
    });

    const response: ApiResponse<ChatResponse> = {
      success: aiResult.success,
      data: aiResult.success ? {
        response: aiResult.response,
        intent: aiResult.intent,
        parameters: aiResult.parameters,
        actions: aiResult.actions,
      } : undefined,
      message: aiResult.success ? 'Chat message processed successfully' : 'Chat processing failed',
      error: aiResult.error,
    };

    res.json(response);
  } catch (error: any) {
    // Hata durumunda veritabanını güncelle
    await chatDoc.update({
      status: 'failed',
      response: 'Üzgünüm, bir hata oluştu.',
    });

    throw error;
  }
});

// Chat geçmişini getir
export const getChatHistory = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const schoolId = req.params.schoolId;
  const userId = req.user!.id;
  const limit = parseInt(req.query.limit as string) || 50;
  const page = parseInt(req.query.page as string) || 1;

  const chatRef = getChatMessagesRef(schoolId);
  
  // Kullanıcının kendi mesajlarını getir
  const query = chatRef
    .where('userId', '==', userId)
    .orderBy('createdAt', 'desc')
    .limit(limit);

  const snapshot = await query.get();
  
  const messages = snapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data(),
    createdAt: doc.data().createdAt.toDate(),
  }));

  const response: ApiResponse = {
    success: true,
    data: {
      messages,
      pagination: {
        page,
        limit,
        total: messages.length,
        hasMore: messages.length === limit,
      },
    },
  };

  res.json(response);
});

// Chat mesajını sil
export const deleteChatMessage = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const schoolId = req.params.schoolId;
  const messageId = req.params.messageId;
  const userId = req.user!.id;

  const chatRef = getChatMessagesRef(schoolId);
  const messageDoc = await chatRef.doc(messageId).get();

  if (!messageDoc.exists) {
    throw new ValidationError('Chat message not found');
  }

  const messageData = messageDoc.data();
  
  // Sadece kendi mesajını silebilir (admin hariç)
  if (messageData!.userId !== userId && req.user!.role !== 'admin') {
    throw new ValidationError('You can only delete your own messages');
  }

  await messageDoc.ref.delete();

  const response: ApiResponse = {
    success: true,
    message: 'Chat message deleted successfully',
  };

  res.json(response);
});

// Chat istatistikleri
export const getChatStats = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const schoolId = req.params.schoolId;
  const userId = req.user!.id;

  const chatRef = getChatMessagesRef(schoolId);
  
  // Son 30 günün istatistikleri
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const [totalSnapshot, recentSnapshot, userSnapshot] = await Promise.all([
    // Toplam mesaj sayısı
    chatRef.count().get(),
    
    // Son 30 gündeki mesajlar
    chatRef.where('createdAt', '>=', thirtyDaysAgo).count().get(),
    
    // Kullanıcının mesajları
    chatRef.where('userId', '==', userId).count().get(),
  ]);

  const response: ApiResponse = {
    success: true,
    data: {
      totalMessages: totalSnapshot.data().count,
      recentMessages: recentSnapshot.data().count,
      userMessages: userSnapshot.data().count,
      period: '30 days',
    },
  };

  res.json(response);
});
