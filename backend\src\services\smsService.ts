import axios from 'axios';
import { SmsResult } from '../types';

class SmsService {
  private netgsmUsername: string;
  private netgsmPassword: string;
  private netgsmHeader: string;
  private netgsmBaseUrl = 'https://api.netgsm.com.tr/sms/send/get';

  constructor() {
    this.netgsmUsername = process.env.NETGSM_USERNAME || '';
    this.netgsmPassword = process.env.NETGSM_PASSWORD || '';
    this.netgsmHeader = process.env.NETGSM_HEADER || '';
    
    if (!this.netgsmUsername || !this.netgsmPassword) {
      console.warn('NetGSM credentials not found in environment variables');
    }
  }

  // Tekil SMS gönderimi
  async sendSms(phone: string, message: string): Promise<SmsResult> {
    try {
      if (!this.netgsmUsername || !this.netgsmPassword) {
        throw new Error('NetGSM credentials not configured');
      }

      // Telefon numarasını temizle (sad<PERSON><PERSON> r<PERSON>)
      const cleanPhone = this.cleanPhoneNumber(phone);
      
      if (!this.isValidPhoneNumber(cleanPhone)) {
        throw new Error('Invalid phone number format');
      }

      const params = new URLSearchParams({
        usercode: this.netgsmUsername,
        password: this.netgsmPassword,
        gsmno: cleanPhone,
        message: message,
        msgheader: this.netgsmHeader,
        filter: '0', // Türkçe karakter filtresi
        startdate: '', // Hemen gönder
        stopdate: '', // Süresiz
      });

      const response = await axios.get(`${this.netgsmBaseUrl}?${params.toString()}`, {
        timeout: 10000,
      });

      // NetGSM yanıt kodlarını kontrol et
      const result = this.parseNetgsmResponse(response.data);
      
      if (result.success) {
        console.log(`SMS sent successfully to ${cleanPhone}: ${result.messageId}`);
      } else {
        console.error(`SMS failed to ${cleanPhone}: ${result.error}`);
      }

      return result;
    } catch (error) {
      console.error('SMS sending error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  // Toplu SMS gönderimi
  async sendBulkSms(phones: string[], message: string): Promise<SmsResult[]> {
    const results: SmsResult[] = [];
    
    for (const phone of phones) {
      const result = await this.sendSms(phone, message);
      results.push(result);
      
      // Rate limiting - her SMS arasında kısa bekleme
      await this.delay(100);
    }
    
    return results;
  }

  // NetGSM yanıt kodlarını parse et
  private parseNetgsmResponse(responseData: string): SmsResult {
    const response = responseData.trim();
    
    // Başarılı yanıt: "00 MSGID"
    if (response.startsWith('00 ')) {
      return {
        success: true,
        messageId: response.substring(3),
      };
    }
    
    // Hata kodları
    const errorMessages: Record<string, string> = {
      '20': 'Mesaj metninde hata var',
      '30': 'Geçersiz kullanıcı adı, şifre veya kullanıcınız aktif değil',
      '40': 'Mesaj başlığınız (header) sistemde tanımlı değil',
      '70': 'Hatalı sorgulama. Gönderdiğiniz parametrelerden birisi hatalı veya zorunlu alanlardan birisi eksik',
    };
    
    return {
      success: false,
      error: errorMessages[response] || `Unknown error: ${response}`,
    };
  }

  // Telefon numarasını temizle
  private cleanPhoneNumber(phone: string): string {
    // Sadece rakamları al
    let cleaned = phone.replace(/\D/g, '');
    
    // Türkiye için +90 veya 0 ile başlıyorsa düzelt
    if (cleaned.startsWith('90')) {
      cleaned = cleaned.substring(2);
    } else if (cleaned.startsWith('0')) {
      cleaned = cleaned.substring(1);
    }
    
    // 5 ile başlayan 10 haneli numara olmalı
    return cleaned;
  }

  // Telefon numarası formatını kontrol et
  private isValidPhoneNumber(phone: string): boolean {
    // Türkiye cep telefonu: 5XXXXXXXXX (10 hane, 5 ile başlar)
    return /^5\d{9}$/.test(phone);
  }

  // Bekleme fonksiyonu
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // SMS karakter sayısını hesapla (Türkçe karakter desteği)
  calculateSmsLength(message: string): { 
    length: number; 
    smsCount: number; 
    encoding: 'GSM7' | 'UCS2';
    remainingChars: number;
  } {
    // Türkçe karakterler varsa UCS2, yoksa GSM7
    const hasUnicodeChars = /[çğıöşüÇĞIİÖŞÜ]/.test(message);
    const encoding = hasUnicodeChars ? 'UCS2' : 'GSM7';
    
    const maxLength = encoding === 'UCS2' ? 70 : 160;
    const maxConcatLength = encoding === 'UCS2' ? 67 : 153;
    
    const length = message.length;
    let smsCount = 1;
    let remainingChars = maxLength - length;
    
    if (length > maxLength) {
      smsCount = Math.ceil(length / maxConcatLength);
      const usedInLastSms = length % maxConcatLength;
      remainingChars = usedInLastSms === 0 ? 0 : maxConcatLength - usedInLastSms;
    }
    
    return {
      length,
      smsCount,
      encoding,
      remainingChars: Math.max(0, remainingChars),
    };
  }

  // SMS önizleme
  previewSms(message: string): {
    message: string;
    stats: {
      length: number;
      smsCount: number;
      encoding: 'GSM7' | 'UCS2';
      remainingChars: number;
    };
    estimatedCost: number;
  } {
    const stats = this.calculateSmsLength(message);
    const estimatedCost = stats.smsCount * 0.05; // Örnek fiyat
    
    return {
      message,
      stats,
      estimatedCost,
    };
  }
}

export const smsService = new SmsService();
export default smsService;
