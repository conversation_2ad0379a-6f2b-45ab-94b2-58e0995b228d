import axios from 'axios';
import { SmsResult } from '../types';

class SmsService {
  private netgsmUsername: string;
  private netgsmPassword: string;
  private netgsmHeader: string;
  private netgsmBaseUrl = 'https://api.netgsm.com.tr/sms/send/get';

  constructor() {
    this.netgsmUsername = process.env.NETGSM_USERNAME || '';
    this.netgsmPassword = process.env.NETGSM_PASSWORD || '';
    this.netgsmHeader = process.env.NETGSM_HEADER || '';
    
    if (!this.netgsmUsername || !this.netgsmPassword) {
      console.warn('NetGSM credentials not found in environment variables');
    }
  }

  // Tekil SMS gönderimi
  async sendSms(phone: string, message: string): Promise<SmsResult> {
    try {
      if (!this.netgsmUsername || !this.netgsmPassword) {
        throw new Error('NetGSM credentials not configured');
      }

      // Telefon numarasını temizle (sad<PERSON><PERSON> r<PERSON>)
      const cleanPhone = this.cleanPhoneNumber(phone);
      
      if (!this.isValidPhoneNumber(cleanPhone)) {
        throw new Error('Invalid phone number format');
      }

      const params = new URLSearchParams({
        usercode: this.netgsmUsername,
        password: this.netgsmPassword,
        gsmno: cleanPhone,
        message: message,
        msgheader: this.netgsmHeader,
        filter: '0', // Türkçe karakter filtresi
        startdate: '', // Hemen gönder
        stopdate: '', // Süresiz
      });

      const response = await axios.get(`${this.netgsmBaseUrl}?${params.toString()}`, {
        timeout: 10000,
      });

      // NetGSM yanıt kodlarını kontrol et
      const result = this.parseNetgsmResponse(response.data);
      
      if (result.success) {
        console.log(`SMS sent successfully to ${cleanPhone}, Message ID: ${result.messageId}`);
      } else {
        console.error(`SMS failed to ${cleanPhone}: ${result.error}`);
      }

      return result;
    } catch (error: any) {
      console.error('SMS Service Error:', error);
      return {
        success: false,
        error: error.message || 'SMS sending failed',
      };
    }
  }

  // Toplu SMS gönderimi
  async sendBulkSms(phones: string[], message: string): Promise<{ 
    successful: number; 
    failed: number; 
    results: Array<{ phone: string; result: SmsResult }> 
  }> {
    const results: Array<{ phone: string; result: SmsResult }> = [];
    let successful = 0;
    let failed = 0;

    // Paralel gönderim yerine sıralı gönderim (rate limiting için)
    for (const phone of phones) {
      const result = await this.sendSms(phone, message);
      results.push({ phone, result });
      
      if (result.success) {
        successful++;
      } else {
        failed++;
      }

      // Rate limiting - her SMS arasında kısa bekleme
      await this.delay(100);
    }

    return {
      successful,
      failed,
      results,
    };
  }

  // Telefon numarasını temizle
  private cleanPhoneNumber(phone: string): string {
    // Sadece rakamları al
    let cleaned = phone.replace(/\D/g, '');
    
    // Türkiye için format kontrolü
    if (cleaned.startsWith('0')) {
      cleaned = '90' + cleaned.substring(1); // 0xxx -> 90xxx
    } else if (!cleaned.startsWith('90')) {
      cleaned = '90' + cleaned; // xxx -> 90xxx
    }
    
    return cleaned;
  }

  // Telefon numarası format kontrolü
  private isValidPhoneNumber(phone: string): boolean {
    // Türkiye telefon numarası: 905xxxxxxxxx (13 haneli)
    return /^905\d{9}$/.test(phone);
  }

  // NetGSM API yanıtını parse et
  private parseNetgsmResponse(responseData: string): SmsResult {
    const response = responseData.trim();
    
    // Başarılı yanıt: "00 XXXXXXXXX" (00 + mesaj ID)
    if (response.startsWith('00 ')) {
      return {
        success: true,
        messageId: response.substring(3),
      };
    }
    
    // Hata kodları
    const errorCodes: { [key: string]: string } = {
      '01': 'Mesaj gövdesi boş',
      '02': 'Mesaj başlığı boş',
      '03': 'Kullanıcı adı veya şifre hatalı',
      '04': 'Mesaj başlığı onaylanmamış',
      '05': 'Mesaj gövdesi çok uzun',
      '06': 'Geçersiz telefon numarası',
      '07': 'Mesaj başlığı çok uzun',
      '08': 'Yetersiz bakiye',
      '09': 'Sistem hatası',
      '10': 'Kullanıcı aktif değil',
      '11': 'API erişimi kapalı',
      '12': 'Geçersiz tarih formatı',
      '13': 'Mesaj gönderim tarihi geçmiş',
      '14': 'Mesaj çok uzun',
      '15': 'Geçersiz mesaj tipi',
    };

    const errorMessage = errorCodes[response] || `Bilinmeyen hata: ${response}`;
    
    return {
      success: false,
      error: errorMessage,
    };
  }

  // Bekleme fonksiyonu
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // SMS karakter sayısını hesapla (Türkçe karakter desteği)
  calculateSmsLength(message: string): {
    length: number;
    smsCount: number;
    encoding: 'GSM7' | 'UCS2';
    remainingChars: number;
  } {
    // Türkçe karakterler varsa UCS2, yoksa GSM7
    const hasUnicodeChars = /[çğıöşüÇĞIİÖŞÜ]/.test(message);
    const encoding = hasUnicodeChars ? 'UCS2' : 'GSM7';

    const maxLength = encoding === 'UCS2' ? 70 : 160;
    const maxConcatLength = encoding === 'UCS2' ? 67 : 153;

    const length = message.length;
    let smsCount = 1;
    let remainingChars = maxLength - length;

    if (length > maxLength) {
      smsCount = Math.ceil(length / maxConcatLength);
      const usedInLastSms = length % maxConcatLength;
      remainingChars = usedInLastSms === 0 ? 0 : maxConcatLength - usedInLastSms;
    }

    return {
      length,
      smsCount,
      encoding,
      remainingChars: Math.max(0, remainingChars),
    };
  }

  // SMS önizleme
  previewSms(message: string): {
    message: string;
    stats: {
      length: number;
      smsCount: number;
      encoding: 'GSM7' | 'UCS2';
      remainingChars: number;
    };
    estimatedCost: number;
  } {
    const stats = this.calculateSmsLength(message);
    const estimatedCost = stats.smsCount * 0.05; // Örnek fiyat
    
    return {
      message,
      stats,
      estimatedCost,
    };
  }
}

export const smsService = new SmsService();
export default smsService;
