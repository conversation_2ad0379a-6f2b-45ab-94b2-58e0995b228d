"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyToken = exports.register = exports.login = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const firebase_1 = require("../config/firebase");
const errorHandler_1 = require("../middleware/errorHandler");
const types_1 = require("../types");
exports.login = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { email, password } = req.body;
    if (!email || !password) {
        throw new types_1.ValidationError('Email and password are required');
    }
    const usersSnapshot = await firebase_1.db
        .collection(firebase_1.collections.users)
        .where('email', '==', email)
        .limit(1)
        .get();
    if (usersSnapshot.empty) {
        throw new types_1.AuthenticationError('Invalid email or password');
    }
    const userDoc = usersSnapshot.docs[0];
    const userData = userDoc.data();
    const isPasswordValid = await bcryptjs_1.default.compare(password, userData.password);
    if (!isPasswordValid) {
        throw new types_1.AuthenticationError('Invalid email or password');
    }
    const jwtSecret = process.env.JWT_SECRET || 'fallback-secret';
    const token = jsonwebtoken_1.default.sign({ userId: userDoc.id }, jwtSecret, { expiresIn: '7d' });
    const { password: _, ...userWithoutPassword } = userData;
    const response = {
        success: true,
        data: {
            user: {
                id: userDoc.id,
                email: userWithoutPassword.email,
                name: userWithoutPassword.name,
                role: userWithoutPassword.role,
                schoolId: userWithoutPassword.schoolId,
                createdAt: userWithoutPassword.createdAt,
                updatedAt: userWithoutPassword.updatedAt,
            },
            token,
        },
        message: 'Login successful',
    };
    res.json(response);
});
exports.register = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { email, password, name, role, schoolId } = req.body;
    if (!email || !password || !name || !role) {
        throw new types_1.ValidationError('Email, password, name, and role are required');
    }
    if (role === 'school_admin' && !schoolId) {
        throw new types_1.ValidationError('School ID is required for school admin');
    }
    const existingUserSnapshot = await firebase_1.db
        .collection(firebase_1.collections.users)
        .where('email', '==', email)
        .limit(1)
        .get();
    if (!existingUserSnapshot.empty) {
        throw new types_1.ValidationError('Email already exists');
    }
    const hashedPassword = await bcryptjs_1.default.hash(password, 12);
    const newUser = {
        email,
        password: hashedPassword,
        name,
        role,
        ...(schoolId && { schoolId }),
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    const userRef = await firebase_1.db.collection(firebase_1.collections.users).add(newUser);
    const { password: _, ...userWithoutPassword } = newUser;
    const response = {
        success: true,
        data: {
            id: userRef.id,
            email: userWithoutPassword.email,
            name: userWithoutPassword.name,
            role: userWithoutPassword.role,
            schoolId: userWithoutPassword.schoolId,
            createdAt: userWithoutPassword.createdAt,
            updatedAt: userWithoutPassword.updatedAt,
        },
        message: 'User created successfully',
    };
    res.status(201).json(response);
});
exports.verifyToken = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];
    if (!token) {
        throw new types_1.AuthenticationError('Access token required');
    }
    try {
        const jwtSecret = process.env.JWT_SECRET || 'fallback-secret';
        const decoded = jsonwebtoken_1.default.verify(token, jwtSecret);
        const userDoc = await firebase_1.db.collection(firebase_1.collections.users).doc(decoded.userId).get();
        if (!userDoc.exists) {
            throw new types_1.AuthenticationError('User not found');
        }
        const userData = userDoc.data();
        const { password: _, ...userWithoutPassword } = userData;
        const response = {
            success: true,
            data: {
                id: userDoc.id,
                email: userWithoutPassword.email,
                name: userWithoutPassword.name,
                role: userWithoutPassword.role,
                schoolId: userWithoutPassword.schoolId,
                createdAt: userWithoutPassword.createdAt,
                updatedAt: userWithoutPassword.updatedAt,
            },
        };
        res.json(response);
    }
    catch (error) {
        throw new types_1.AuthenticationError('Invalid or expired token');
    }
});
//# sourceMappingURL=authController.js.map