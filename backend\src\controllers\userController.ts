import { Response } from 'express';
import bcrypt from 'bcryptjs';
import { AuthenticatedRequest, ValidationError, NotFoundError } from '../types';
import { asyncHandler } from '../middleware/errorHandler';
import { db, collections } from '../config/firebase';
import type { User } from '../../../shared/types';
import type { ApiResponse } from '../types';

// Tüm kullanıcıları getir (sadece admin)
export const getUsers = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const limit = parseInt(req.query.limit as string) || 50;
  const role = req.query.role as string;

  let query = db.collection(collections.users).orderBy('name').limit(limit);

  // Role filtresi
  if (role) {
    query = query.where('role', '==', role);
  }

  const snapshot = await query.get();
  
  const users = snapshot.docs.map(doc => {
    const data = doc.data();
    const { password: _, ...userWithoutPassword } = data;
    return {
      id: doc.id,
      ...userWithoutPassword,
      createdAt: data.createdAt.toDate(),
      updatedAt: data.updatedAt.toDate(),
    };
  });

  const response: ApiResponse = {
    success: true,
    data: { users },
  };

  res.json(response);
});

// Tek kullanıcı getir
export const getUser = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.params.userId;
  
  const userDoc = await db.collection(collections.users).doc(userId).get();
  
  if (!userDoc.exists) {
    throw new NotFoundError('User not found');
  }

  const userData = userDoc.data()!;
  const { password: _, ...userWithoutPassword } = userData;
  
  const response: ApiResponse = {
    success: true,
    data: {
      id: userDoc.id,
      ...userWithoutPassword,
      createdAt: userData.createdAt.toDate(),
      updatedAt: userData.updatedAt.toDate(),
    },
  };

  res.json(response);
});

// Kullanıcı güncelle
export const updateUser = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.params.userId;
  const { name, email, role, schoolId } = req.body;

  const userDoc = await db.collection(collections.users).doc(userId).get();
  
  if (!userDoc.exists) {
    throw new NotFoundError('User not found');
  }

  // Güncelleme verilerini hazırla
  const updateData: any = {
    updatedAt: new Date(),
  };

  if (name) updateData.name = name;
  if (email) updateData.email = email;
  if (role) updateData.role = role;
  if (schoolId !== undefined) updateData.schoolId = schoolId;

  // Email değişiyorsa, aynı email'e sahip başka kullanıcı var mı kontrol et
  if (email && email !== userDoc.data()!.email) {
    const existingUserSnapshot = await db
      .collection(collections.users)
      .where('email', '==', email)
      .limit(1)
      .get();

    if (!existingUserSnapshot.empty) {
      throw new ValidationError('Email already exists');
    }
  }

  // School admin ise schoolId gerekli
  if (role === 'school_admin' && !schoolId) {
    throw new ValidationError('School ID is required for school admin');
  }

  await userDoc.ref.update(updateData);

  const response: ApiResponse = {
    success: true,
    message: 'User updated successfully',
  };

  res.json(response);
});

// Kullanıcı sil (sadece admin)
export const deleteUser = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.params.userId;

  // Kendi hesabını silemez
  if (userId === req.user!.id) {
    throw new ValidationError('You cannot delete your own account');
  }

  const userDoc = await db.collection(collections.users).doc(userId).get();
  
  if (!userDoc.exists) {
    throw new NotFoundError('User not found');
  }

  await userDoc.ref.delete();

  const response: ApiResponse = {
    success: true,
    message: 'User deleted successfully',
  };

  res.json(response);
});

// Şifre değiştir
export const changePassword = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.params.userId;
  const { currentPassword, newPassword } = req.body;

  if (!currentPassword || !newPassword) {
    throw new ValidationError('Current password and new password are required');
  }

  if (newPassword.length < 6) {
    throw new ValidationError('New password must be at least 6 characters');
  }

  // Sadece kendi şifresini değiştirebilir (admin hariç)
  if (userId !== req.user!.id && req.user!.role !== 'admin') {
    throw new ValidationError('You can only change your own password');
  }

  const userDoc = await db.collection(collections.users).doc(userId).get();
  
  if (!userDoc.exists) {
    throw new NotFoundError('User not found');
  }

  const userData = userDoc.data()!;

  // Mevcut şifreyi kontrol et (admin değilse)
  if (req.user!.role !== 'admin') {
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, userData.password);
    if (!isCurrentPasswordValid) {
      throw new ValidationError('Current password is incorrect');
    }
  }

  // Yeni şifreyi hash'le
  const hashedNewPassword = await bcrypt.hash(newPassword, 12);

  await userDoc.ref.update({
    password: hashedNewPassword,
    updatedAt: new Date(),
  });

  const response: ApiResponse = {
    success: true,
    message: 'Password changed successfully',
  };

  res.json(response);
});

// Okula bağlı kullanıcıları getir
export const getUsersBySchool = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const schoolId = req.params.schoolId;

  const snapshot = await db
    .collection(collections.users)
    .where('schoolId', '==', schoolId)
    .orderBy('name')
    .get();
  
  const users = snapshot.docs.map(doc => {
    const data = doc.data();
    const { password: _, ...userWithoutPassword } = data;
    return {
      id: doc.id,
      ...userWithoutPassword,
      createdAt: data.createdAt.toDate(),
      updatedAt: data.updatedAt.toDate(),
    };
  });

  const response: ApiResponse = {
    success: true,
    data: { users },
  };

  res.json(response);
});

// Kullanıcı istatistikleri
export const getUserStats = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const [totalSnapshot, adminSnapshot, schoolAdminSnapshot] = await Promise.all([
    db.collection(collections.users).count().get(),
    db.collection(collections.users).where('role', '==', 'admin').count().get(),
    db.collection(collections.users).where('role', '==', 'school_admin').count().get(),
  ]);

  const response: ApiResponse = {
    success: true,
    data: {
      total: totalSnapshot.data().count,
      admins: adminSnapshot.data().count,
      schoolAdmins: schoolAdminSnapshot.data().count,
    },
  };

  res.json(response);
});
