import { AiEnhanceResult, ChatResult } from '../types';
declare class AiService {
    private geminiApiKey;
    private geminiBaseUrl;
    constructor();
    enhanceMessage(originalMessage: string, context?: string): Promise<AiEnhanceResult>;
    processChatMessage(message: string, context?: any): Promise<ChatResult>;
    private buildEnhancePrompt;
    private buildChatPrompt;
    private parseChatResponse;
}
export declare const aiService: AiService;
export default aiService;
//# sourceMappingURL=aiService.d.ts.map