"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getChatMessagesRef = exports.getSmsMessagesRef = exports.getClassesRef = exports.getStudentsRef = exports.getSchoolRef = exports.collections = exports.db = exports.firebaseAdmin = void 0;
const firebase_admin_1 = __importDefault(require("firebase-admin"));
const firestore_1 = require("firebase-admin/firestore");
const initializeFirebase = () => {
    if (firebase_admin_1.default.apps.length === 0) {
        if (!process.env.FIREBASE_PROJECT_ID || !process.env.FIREBASE_PRIVATE_KEY) {
            console.warn('Firebase credentials not found. Running in demo mode.');
            return firebase_admin_1.default;
        }
        try {
            const serviceAccount = {
                type: 'service_account',
                project_id: process.env.FIREBASE_PROJECT_ID,
                private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
                private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
                client_email: process.env.FIREBASE_CLIENT_EMAIL,
                client_id: process.env.FIREBASE_CLIENT_ID,
                auth_uri: process.env.FIREBASE_AUTH_URI,
                token_uri: process.env.FIREBASE_TOKEN_URI,
            };
            firebase_admin_1.default.initializeApp({
                credential: firebase_admin_1.default.credential.cert(serviceAccount),
                projectId: process.env.FIREBASE_PROJECT_ID,
            });
            console.log('Firebase initialized successfully');
        }
        catch (error) {
            console.error('Firebase initialization failed:', error);
            console.warn('Running in demo mode without Firebase.');
        }
    }
    return firebase_admin_1.default;
};
exports.firebaseAdmin = initializeFirebase();
exports.db = (0, firestore_1.getFirestore)();
exports.collections = {
    users: 'users',
    schools: 'schools',
    students: 'students',
    classes: 'classes',
    smsMessages: 'smsMessages',
    chatMessages: 'chatMessages',
    smsTemplates: 'smsTemplates',
};
const getSchoolRef = (schoolId) => {
    return exports.db.collection(exports.collections.schools).doc(schoolId);
};
exports.getSchoolRef = getSchoolRef;
const getStudentsRef = (schoolId) => {
    return (0, exports.getSchoolRef)(schoolId).collection(exports.collections.students);
};
exports.getStudentsRef = getStudentsRef;
const getClassesRef = (schoolId) => {
    return (0, exports.getSchoolRef)(schoolId).collection(exports.collections.classes);
};
exports.getClassesRef = getClassesRef;
const getSmsMessagesRef = (schoolId) => {
    return (0, exports.getSchoolRef)(schoolId).collection(exports.collections.smsMessages);
};
exports.getSmsMessagesRef = getSmsMessagesRef;
const getChatMessagesRef = (schoolId) => {
    return (0, exports.getSchoolRef)(schoolId).collection(exports.collections.chatMessages);
};
exports.getChatMessagesRef = getChatMessagesRef;
//# sourceMappingURL=firebase.js.map