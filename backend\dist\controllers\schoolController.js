"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSchoolStats = exports.deleteSchool = exports.updateSchool = exports.createSchool = exports.getSchool = exports.getSchools = void 0;
const types_1 = require("../types");
const errorHandler_1 = require("../middleware/errorHandler");
const firebase_1 = require("../config/firebase");
exports.getSchools = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const snapshot = await firebase_1.db.collection(firebase_1.collections.schools).orderBy('name').get();
    const schools = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt.toDate(),
        updatedAt: doc.data().updatedAt.toDate(),
    }));
    const response = {
        success: true,
        data: { schools },
    };
    res.json(response);
});
exports.getSchool = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const schoolId = req.params.schoolId;
    const schoolDoc = await firebase_1.db.collection(firebase_1.collections.schools).doc(schoolId).get();
    if (!schoolDoc.exists) {
        throw new types_1.NotFoundError('School not found');
    }
    const schoolData = schoolDoc.data();
    const response = {
        success: true,
        data: {
            id: schoolDoc.id,
            ...schoolData,
            createdAt: schoolData.createdAt.toDate(),
            updatedAt: schoolData.updatedAt.toDate(),
        },
    };
    res.json(response);
});
exports.createSchool = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { name, address, phone, email, adminId } = req.body;
    if (!name || !address || !phone || !email || !adminId) {
        throw new types_1.ValidationError('Name, address, phone, email, and adminId are required');
    }
    const adminDoc = await firebase_1.db.collection(firebase_1.collections.users).doc(adminId).get();
    if (!adminDoc.exists) {
        throw new types_1.ValidationError('Admin user not found');
    }
    const adminData = adminDoc.data();
    if (adminData.role !== 'school_admin') {
        throw new types_1.ValidationError('User must have school_admin role');
    }
    const existingSchoolSnapshot = await firebase_1.db
        .collection(firebase_1.collections.schools)
        .where('name', '==', name)
        .limit(1)
        .get();
    if (!existingSchoolSnapshot.empty) {
        throw new types_1.ValidationError('School with this name already exists');
    }
    const newSchool = {
        name,
        address,
        phone,
        email,
        adminId,
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    const schoolRef = await firebase_1.db.collection(firebase_1.collections.schools).add(newSchool);
    await adminDoc.ref.update({
        schoolId: schoolRef.id,
        updatedAt: new Date(),
    });
    const response = {
        success: true,
        data: {
            id: schoolRef.id,
            ...newSchool,
        },
        message: 'School created successfully',
    };
    res.status(201).json(response);
});
exports.updateSchool = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const schoolId = req.params.schoolId;
    const { name, address, phone, email } = req.body;
    const schoolDoc = await firebase_1.db.collection(firebase_1.collections.schools).doc(schoolId).get();
    if (!schoolDoc.exists) {
        throw new types_1.NotFoundError('School not found');
    }
    const updateData = {
        updatedAt: new Date(),
    };
    if (name)
        updateData.name = name;
    if (address)
        updateData.address = address;
    if (phone)
        updateData.phone = phone;
    if (email)
        updateData.email = email;
    if (name && name !== schoolDoc.data().name) {
        const existingSchoolSnapshot = await firebase_1.db
            .collection(firebase_1.collections.schools)
            .where('name', '==', name)
            .limit(1)
            .get();
        if (!existingSchoolSnapshot.empty) {
            throw new types_1.ValidationError('School with this name already exists');
        }
    }
    await schoolDoc.ref.update(updateData);
    const response = {
        success: true,
        message: 'School updated successfully',
    };
    res.json(response);
});
exports.deleteSchool = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const schoolId = req.params.schoolId;
    const schoolDoc = await firebase_1.db.collection(firebase_1.collections.schools).doc(schoolId).get();
    if (!schoolDoc.exists) {
        throw new types_1.NotFoundError('School not found');
    }
    const usersSnapshot = await firebase_1.db
        .collection(firebase_1.collections.users)
        .where('schoolId', '==', schoolId)
        .get();
    const batch = firebase_1.db.batch();
    usersSnapshot.docs.forEach(userDoc => {
        batch.update(userDoc.ref, {
            schoolId: null,
            updatedAt: new Date(),
        });
    });
    batch.delete(schoolDoc.ref);
    await batch.commit();
    const response = {
        success: true,
        message: 'School deleted successfully',
    };
    res.json(response);
});
exports.getSchoolStats = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const schoolId = req.params.schoolId;
    const [studentsSnapshot, classesSnapshot, smsSnapshot] = await Promise.all([
        firebase_1.db.collection(firebase_1.collections.schools).doc(schoolId).collection(firebase_1.collections.students).count().get(),
        firebase_1.db.collection(firebase_1.collections.schools).doc(schoolId).collection(firebase_1.collections.classes).count().get(),
        firebase_1.db.collection(firebase_1.collections.schools).doc(schoolId).collection(firebase_1.collections.smsMessages).count().get(),
    ]);
    const response = {
        success: true,
        data: {
            totalStudents: studentsSnapshot.data().count,
            totalClasses: classesSnapshot.data().count,
            totalSmsMessages: smsSnapshot.data().count,
        },
    };
    res.json(response);
});
//# sourceMappingURL=schoolController.js.map