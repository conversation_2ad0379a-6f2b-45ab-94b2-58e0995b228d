import { SmsResult } from '../types';
declare class SmsService {
    private netgsmUsername;
    private netgsmPassword;
    private netgsmHeader;
    private netgsmBaseUrl;
    constructor();
    sendSms(phone: string, message: string): Promise<SmsResult>;
    sendBulkSms(phones: string[], message: string): Promise<SmsResult[]>;
    private parseNetgsmResponse;
    private cleanPhoneNumber;
    private isValidPhoneNumber;
    private delay;
    calculateSmsLength(message: string): {
        length: number;
        smsCount: number;
        encoding: 'GSM7' | 'UCS2';
        remainingChars: number;
    };
    previewSms(message: string): {
        message: string;
        stats: {
            length: number;
            smsCount: number;
            encoding: 'GSM7' | 'UCS2';
            remainingChars: number;
        };
        estimatedCost: number;
    };
}
export declare const smsService: SmsService;
export default smsService;
//# sourceMappingURL=smsService.d.ts.map