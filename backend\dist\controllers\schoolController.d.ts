import { Response } from 'express';
export declare const getSchools: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const getSchool: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const createSchool: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const updateSchool: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const deleteSchool: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const getSchoolStats: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
//# sourceMappingURL=schoolController.d.ts.map