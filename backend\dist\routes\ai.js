"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const aiController_1 = require("../controllers/aiController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
router.post('/enhance', auth_1.authenticateToken, aiController_1.enhanceMessage);
router.post('/schools/:schoolId/chat', auth_1.authenticateToken, auth_1.requireSchoolAccess, aiController_1.sendChatMessage);
router.get('/schools/:schoolId/chat/history', auth_1.authenticateToken, auth_1.requireSchoolAccess, aiController_1.getChatHistory);
router.delete('/schools/:schoolId/chat/:messageId', auth_1.authenticateToken, auth_1.requireSchoolAccess, aiController_1.deleteChatMessage);
router.get('/schools/:schoolId/chat/stats', auth_1.authenticateToken, auth_1.requireSchoolAccess, aiController_1.getChatStats);
exports.default = router;
//# sourceMappingURL=ai.js.map