"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserStats = exports.getUsersBySchool = exports.changePassword = exports.deleteUser = exports.updateUser = exports.getUser = exports.getUsers = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const types_1 = require("../types");
const errorHandler_1 = require("../middleware/errorHandler");
const firebase_1 = require("../config/firebase");
exports.getUsers = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const limit = parseInt(req.query.limit) || 50;
    const role = req.query.role;
    let query = firebase_1.db.collection(firebase_1.collections.users).orderBy('name').limit(limit);
    if (role) {
        query = query.where('role', '==', role);
    }
    const snapshot = await query.get();
    const users = snapshot.docs.map(doc => {
        const data = doc.data();
        const { password: _, ...userWithoutPassword } = data;
        return {
            id: doc.id,
            ...userWithoutPassword,
            createdAt: data.createdAt.toDate(),
            updatedAt: data.updatedAt.toDate(),
        };
    });
    const response = {
        success: true,
        data: { users },
    };
    res.json(response);
});
exports.getUser = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.params.userId;
    const userDoc = await firebase_1.db.collection(firebase_1.collections.users).doc(userId).get();
    if (!userDoc.exists) {
        throw new types_1.NotFoundError('User not found');
    }
    const userData = userDoc.data();
    const { password: _, ...userWithoutPassword } = userData;
    const response = {
        success: true,
        data: {
            id: userDoc.id,
            ...userWithoutPassword,
            createdAt: userData.createdAt.toDate(),
            updatedAt: userData.updatedAt.toDate(),
        },
    };
    res.json(response);
});
exports.updateUser = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.params.userId;
    const { name, email, role, schoolId } = req.body;
    const userDoc = await firebase_1.db.collection(firebase_1.collections.users).doc(userId).get();
    if (!userDoc.exists) {
        throw new types_1.NotFoundError('User not found');
    }
    const updateData = {
        updatedAt: new Date(),
    };
    if (name)
        updateData.name = name;
    if (email)
        updateData.email = email;
    if (role)
        updateData.role = role;
    if (schoolId !== undefined)
        updateData.schoolId = schoolId;
    if (email && email !== userDoc.data().email) {
        const existingUserSnapshot = await firebase_1.db
            .collection(firebase_1.collections.users)
            .where('email', '==', email)
            .limit(1)
            .get();
        if (!existingUserSnapshot.empty) {
            throw new types_1.ValidationError('Email already exists');
        }
    }
    if (role === 'school_admin' && !schoolId) {
        throw new types_1.ValidationError('School ID is required for school admin');
    }
    await userDoc.ref.update(updateData);
    const response = {
        success: true,
        message: 'User updated successfully',
    };
    res.json(response);
});
exports.deleteUser = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.params.userId;
    if (userId === req.user.id) {
        throw new types_1.ValidationError('You cannot delete your own account');
    }
    const userDoc = await firebase_1.db.collection(firebase_1.collections.users).doc(userId).get();
    if (!userDoc.exists) {
        throw new types_1.NotFoundError('User not found');
    }
    await userDoc.ref.delete();
    const response = {
        success: true,
        message: 'User deleted successfully',
    };
    res.json(response);
});
exports.changePassword = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.params.userId;
    const { currentPassword, newPassword } = req.body;
    if (!currentPassword || !newPassword) {
        throw new types_1.ValidationError('Current password and new password are required');
    }
    if (newPassword.length < 6) {
        throw new types_1.ValidationError('New password must be at least 6 characters');
    }
    if (userId !== req.user.id && req.user.role !== 'admin') {
        throw new types_1.ValidationError('You can only change your own password');
    }
    const userDoc = await firebase_1.db.collection(firebase_1.collections.users).doc(userId).get();
    if (!userDoc.exists) {
        throw new types_1.NotFoundError('User not found');
    }
    const userData = userDoc.data();
    if (req.user.role !== 'admin') {
        const isCurrentPasswordValid = await bcryptjs_1.default.compare(currentPassword, userData.password);
        if (!isCurrentPasswordValid) {
            throw new types_1.ValidationError('Current password is incorrect');
        }
    }
    const hashedNewPassword = await bcryptjs_1.default.hash(newPassword, 12);
    await userDoc.ref.update({
        password: hashedNewPassword,
        updatedAt: new Date(),
    });
    const response = {
        success: true,
        message: 'Password changed successfully',
    };
    res.json(response);
});
exports.getUsersBySchool = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const schoolId = req.params.schoolId;
    const snapshot = await firebase_1.db
        .collection(firebase_1.collections.users)
        .where('schoolId', '==', schoolId)
        .orderBy('name')
        .get();
    const users = snapshot.docs.map(doc => {
        const data = doc.data();
        const { password: _, ...userWithoutPassword } = data;
        return {
            id: doc.id,
            ...userWithoutPassword,
            createdAt: data.createdAt.toDate(),
            updatedAt: data.updatedAt.toDate(),
        };
    });
    const response = {
        success: true,
        data: { users },
    };
    res.json(response);
});
exports.getUserStats = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const [totalSnapshot, adminSnapshot, schoolAdminSnapshot] = await Promise.all([
        firebase_1.db.collection(firebase_1.collections.users).count().get(),
        firebase_1.db.collection(firebase_1.collections.users).where('role', '==', 'admin').count().get(),
        firebase_1.db.collection(firebase_1.collections.users).where('role', '==', 'school_admin').count().get(),
    ]);
    const response = {
        success: true,
        data: {
            total: totalSnapshot.data().count,
            admins: adminSnapshot.data().count,
            schoolAdmins: schoolAdminSnapshot.data().count,
        },
    };
    res.json(response);
});
//# sourceMappingURL=userController.js.map