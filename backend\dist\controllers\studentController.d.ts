import { Response } from 'express';
export declare const getStudents: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const getStudent: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const createStudent: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const updateStudent: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const deleteStudent: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const importStudents: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
//# sourceMappingURL=studentController.d.ts.map