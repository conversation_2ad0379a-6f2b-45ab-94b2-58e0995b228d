import { Request } from 'express';
export interface AuthenticatedRequest extends Request {
    user?: {
        id: string;
        email: string;
        role: 'admin' | 'school_admin';
        schoolId?: string;
    };
}
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    error?: string;
}
export interface FirebaseUser {
    uid: string;
    email?: string;
    displayName?: string;
}
export interface SmsResult {
    success: boolean;
    messageId?: string;
    error?: string;
}
export interface AiEnhanceResult {
    originalMessage: string;
    enhancedMessage: string;
    success: boolean;
    error?: string;
}
export interface ChatResult {
    response: string;
    intent?: string;
    parameters?: Record<string, any>;
    actions?: Array<{
        type: string;
        description: string;
        requiresConfirmation: boolean;
    }>;
    success: boolean;
    error?: string;
}
export interface QueryOptions {
    limit?: number;
    offset?: number;
    orderBy?: string;
    orderDirection?: 'asc' | 'desc';
    filters?: Record<string, any>;
}
export declare class AppError extends Error {
    statusCode: number;
    isOperational: boolean;
    constructor(message: string, statusCode: number);
}
export declare class ValidationError extends AppError {
    constructor(message: string);
}
export declare class AuthenticationError extends AppError {
    constructor(message?: string);
}
export declare class AuthorizationError extends AppError {
    constructor(message?: string);
}
export declare class NotFoundError extends AppError {
    constructor(message?: string);
}
//# sourceMappingURL=index.d.ts.map