"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSmsTemplates = exports.previewSms = exports.getSmsStats = exports.getSmsHistory = exports.sendSms = void 0;
const types_1 = require("../types");
const errorHandler_1 = require("../middleware/errorHandler");
const smsService_1 = require("../services/smsService");
const aiService_1 = require("../services/aiService");
const firebase_1 = require("../config/firebase");
const types_2 = require("../../../shared/types");
exports.sendSms = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { recipientType, recipientId, message, useAiEnhancement } = req.body;
    const schoolId = req.params.schoolId;
    const senderId = req.user.id;
    if (!message || message.trim().length === 0) {
        throw new types_1.ValidationError('Message is required');
    }
    if (message.length > 1000) {
        throw new types_1.ValidationError('Message is too long (max 1000 characters)');
    }
    let finalMessage = message.trim();
    let originalMessage = message.trim();
    if (useAiEnhancement) {
        const enhanceResult = await aiService_1.aiService.enhanceMessage(message.trim());
        if (enhanceResult.success) {
            finalMessage = enhanceResult.enhancedMessage;
        }
    }
    const recipients = await getRecipients(schoolId, recipientType, recipientId);
    if (recipients.length === 0) {
        throw new types_1.ValidationError('No recipients found');
    }
    const smsResults = await smsService_1.smsService.sendBulkSms(recipients.map(r => r.phone), finalMessage);
    const smsRef = (0, firebase_1.getSmsMessagesRef)(schoolId);
    const batch = firebase_1.db.batch();
    recipients.forEach((recipient, index) => {
        const result = smsResults[index];
        const smsDoc = smsRef.doc();
        batch.set(smsDoc, {
            schoolId,
            senderId,
            recipientType,
            recipientId: recipientId || null,
            recipientPhone: recipient.phone,
            recipientName: recipient.name,
            message: finalMessage,
            originalMessage: useAiEnhancement ? originalMessage : null,
            status: result.success ? 'sent' : 'failed',
            messageId: result.messageId || null,
            error: result.error || null,
            sentAt: result.success ? new Date() : null,
            createdAt: new Date(),
            updatedAt: new Date(),
        });
    });
    await batch.commit();
    const successful = smsResults.filter(r => r.success).length;
    const failed = smsResults.length - successful;
    const response = {
        success: true,
        data: {
            totalRecipients: recipients.length,
            successful,
            failed,
            message: finalMessage,
            originalMessage: useAiEnhancement ? originalMessage : null,
            enhanced: useAiEnhancement,
        },
        message: `SMS sent to ${successful} recipients successfully`,
    };
    res.json(response);
});
exports.getSmsHistory = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const schoolId = req.params.schoolId;
    const limit = parseInt(req.query.limit) || 50;
    const page = parseInt(req.query.page) || 1;
    const status = req.query.status;
    const recipientType = req.query.recipientType;
    let query = (0, firebase_1.getSmsMessagesRef)(schoolId)
        .orderBy('createdAt', 'desc')
        .limit(limit);
    if (status) {
        query = query.where('status', '==', status);
    }
    if (recipientType) {
        query = query.where('recipientType', '==', recipientType);
    }
    const snapshot = await query.get();
    const messages = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt.toDate(),
        sentAt: doc.data().sentAt?.toDate() || null,
        updatedAt: doc.data().updatedAt.toDate(),
    }));
    const response = {
        success: true,
        data: {
            messages,
            pagination: {
                page,
                limit,
                total: messages.length,
                hasMore: messages.length === limit,
            },
        },
    };
    res.json(response);
});
exports.getSmsStats = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const schoolId = req.params.schoolId;
    const period = req.query.period || '30';
    const periodDays = parseInt(period);
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - periodDays);
    const smsRef = (0, firebase_1.getSmsMessagesRef)(schoolId);
    const [totalSnapshot, sentSnapshot, failedSnapshot, recentSnapshot] = await Promise.all([
        smsRef.count().get(),
        smsRef.where('status', '==', 'sent').count().get(),
        smsRef.where('status', '==', 'failed').count().get(),
        smsRef.where('createdAt', '>=', startDate).count().get(),
    ]);
    const response = {
        success: true,
        data: {
            total: totalSnapshot.data().count,
            sent: sentSnapshot.data().count,
            failed: failedSnapshot.data().count,
            recent: recentSnapshot.data().count,
            period: `${periodDays} days`,
            successRate: totalSnapshot.data().count > 0
                ? ((sentSnapshot.data().count / totalSnapshot.data().count) * 100).toFixed(2)
                : '0',
        },
    };
    res.json(response);
});
exports.previewSms = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { message } = req.body;
    if (!message || message.trim().length === 0) {
        throw new types_1.ValidationError('Message is required');
    }
    const preview = smsService_1.smsService.previewSms(message.trim());
    const response = {
        success: true,
        data: preview,
    };
    res.json(response);
});
exports.getSmsTemplates = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const response = {
        success: true,
        data: {
            templates: types_2.SMS_TEMPLATES,
        },
    };
    res.json(response);
});
async function getRecipients(schoolId, recipientType, recipientId) {
    const recipients = [];
    switch (recipientType) {
        case 'individual':
            if (!recipientId) {
                throw new types_1.ValidationError('Student ID is required for individual SMS');
            }
            const studentDoc = await (0, firebase_1.getStudentsRef)(schoolId).doc(recipientId).get();
            if (!studentDoc.exists) {
                throw new types_1.NotFoundError('Student not found');
            }
            const studentData = studentDoc.data();
            recipients.push({
                phone: studentData.parentPhone,
                name: studentData.parentName || `${studentData.firstName} ${studentData.lastName} Velisi`,
            });
            break;
        case 'class':
            if (!recipientId) {
                throw new types_1.ValidationError('Class name is required for class SMS');
            }
            const classStudentsSnapshot = await (0, firebase_1.getStudentsRef)(schoolId)
                .where('className', '==', recipientId)
                .get();
            classStudentsSnapshot.docs.forEach(doc => {
                const data = doc.data();
                recipients.push({
                    phone: data.parentPhone,
                    name: data.parentName || `${data.firstName} ${data.lastName} Velisi`,
                });
            });
            break;
        case 'school':
            const allStudentsSnapshot = await (0, firebase_1.getStudentsRef)(schoolId).get();
            allStudentsSnapshot.docs.forEach(doc => {
                const data = doc.data();
                recipients.push({
                    phone: data.parentPhone,
                    name: data.parentName || `${data.firstName} ${data.lastName} Velisi`,
                });
            });
            break;
        default:
            throw new types_1.ValidationError('Invalid recipient type');
    }
    return recipients.filter(r => r.phone && r.phone.trim().length > 0);
}
//# sourceMappingURL=smsController.js.map