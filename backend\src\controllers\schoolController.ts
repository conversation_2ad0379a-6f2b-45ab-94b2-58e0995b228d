import { Response } from 'express';
import { AuthenticatedRequest, ValidationError, NotFoundError } from '../types';
import { asyncHandler } from '../middleware/errorHandler';
import { db, collections } from '../config/firebase';
import type { School } from '../../../shared/types';
import type { ApiResponse } from '../types';

// Tüm okulları getir (sadece admin)
export const getSchools = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const snapshot = await db.collection(collections.schools).orderBy('name').get();
  
  const schools = snapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data(),
    createdAt: doc.data().createdAt.toDate(),
    updatedAt: doc.data().updatedAt.toDate(),
  }));

  const response: ApiResponse = {
    success: true,
    data: { schools },
  };

  res.json(response);
});

// Tek okul getir
export const getSchool = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const schoolId = req.params.schoolId;
  
  const schoolDoc = await db.collection(collections.schools).doc(schoolId).get();
  
  if (!schoolDoc.exists) {
    throw new NotFoundError('School not found');
  }

  const schoolData = schoolDoc.data()!;
  
  const response: ApiResponse = {
    success: true,
    data: {
      id: schoolDoc.id,
      ...schoolData,
      createdAt: schoolData.createdAt.toDate(),
      updatedAt: schoolData.updatedAt.toDate(),
    },
  };

  res.json(response);
});

// Yeni okul oluştur (sadece admin)
export const createSchool = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { name, address, phone, email, adminId } = req.body;

  // Validation
  if (!name || !address || !phone || !email || !adminId) {
    throw new ValidationError('Name, address, phone, email, and adminId are required');
  }

  // Admin kullanıcısının var olduğunu kontrol et
  const adminDoc = await db.collection(collections.users).doc(adminId).get();
  if (!adminDoc.exists) {
    throw new ValidationError('Admin user not found');
  }

  const adminData = adminDoc.data()!;
  if (adminData.role !== 'school_admin') {
    throw new ValidationError('User must have school_admin role');
  }

  // Aynı isimde okul var mı kontrol et
  const existingSchoolSnapshot = await db
    .collection(collections.schools)
    .where('name', '==', name)
    .limit(1)
    .get();

  if (!existingSchoolSnapshot.empty) {
    throw new ValidationError('School with this name already exists');
  }

  // Yeni okul oluştur
  const newSchool = {
    name,
    address,
    phone,
    email,
    adminId,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const schoolRef = await db.collection(collections.schools).add(newSchool);

  // Admin kullanıcısını bu okula bağla
  await adminDoc.ref.update({
    schoolId: schoolRef.id,
    updatedAt: new Date(),
  });

  const response: ApiResponse = {
    success: true,
    data: {
      id: schoolRef.id,
      ...newSchool,
    },
    message: 'School created successfully',
  };

  res.status(201).json(response);
});

// Okul güncelle
export const updateSchool = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const schoolId = req.params.schoolId;
  const { name, address, phone, email } = req.body;

  const schoolDoc = await db.collection(collections.schools).doc(schoolId).get();
  
  if (!schoolDoc.exists) {
    throw new NotFoundError('School not found');
  }

  // Güncelleme verilerini hazırla
  const updateData: any = {
    updatedAt: new Date(),
  };

  if (name) updateData.name = name;
  if (address) updateData.address = address;
  if (phone) updateData.phone = phone;
  if (email) updateData.email = email;

  // Aynı isimde başka okul var mı kontrol et (sadece isim değişiyorsa)
  if (name && name !== schoolDoc.data()!.name) {
    const existingSchoolSnapshot = await db
      .collection(collections.schools)
      .where('name', '==', name)
      .limit(1)
      .get();

    if (!existingSchoolSnapshot.empty) {
      throw new ValidationError('School with this name already exists');
    }
  }

  await schoolDoc.ref.update(updateData);

  const response: ApiResponse = {
    success: true,
    message: 'School updated successfully',
  };

  res.json(response);
});

// Okul sil (sadece admin)
export const deleteSchool = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const schoolId = req.params.schoolId;

  const schoolDoc = await db.collection(collections.schools).doc(schoolId).get();
  
  if (!schoolDoc.exists) {
    throw new NotFoundError('School not found');
  }

  // Okula bağlı kullanıcıları güncelle
  const usersSnapshot = await db
    .collection(collections.users)
    .where('schoolId', '==', schoolId)
    .get();

  const batch = db.batch();

  // Kullanıcıların schoolId'sini kaldır
  usersSnapshot.docs.forEach(userDoc => {
    batch.update(userDoc.ref, {
      schoolId: null,
      updatedAt: new Date(),
    });
  });

  // Okulu sil
  batch.delete(schoolDoc.ref);

  await batch.commit();

  const response: ApiResponse = {
    success: true,
    message: 'School deleted successfully',
  };

  res.json(response);
});

// Okul istatistikleri
export const getSchoolStats = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const schoolId = req.params.schoolId;

  // Paralel olarak istatistikleri al
  const [studentsSnapshot, classesSnapshot, smsSnapshot] = await Promise.all([
    db.collection(collections.schools).doc(schoolId).collection(collections.students).count().get(),
    db.collection(collections.schools).doc(schoolId).collection(collections.classes).count().get(),
    db.collection(collections.schools).doc(schoolId).collection(collections.smsMessages).count().get(),
  ]);

  const response: ApiResponse = {
    success: true,
    data: {
      totalStudents: studentsSnapshot.data().count,
      totalClasses: classesSnapshot.data().count,
      totalSmsMessages: smsSnapshot.data().count,
    },
  };

  res.json(response);
});
