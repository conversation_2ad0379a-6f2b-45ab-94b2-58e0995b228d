"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getChatStats = exports.deleteChatMessage = exports.getChatHistory = exports.sendChatMessage = exports.enhanceMessage = void 0;
const types_1 = require("../types");
const errorHandler_1 = require("../middleware/errorHandler");
const aiService_1 = require("../services/aiService");
const firebase_1 = require("../config/firebase");
exports.enhanceMessage = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { message, context } = req.body;
    if (!message || message.trim().length === 0) {
        throw new types_1.ValidationError('Message is required');
    }
    if (message.length > 500) {
        throw new types_1.ValidationError('Message is too long (max 500 characters)');
    }
    const result = await aiService_1.aiService.enhanceMessage(message.trim(), context);
    const response = {
        success: result.success,
        data: result.success ? {
            originalMessage: result.originalMessage,
            enhancedMessage: result.enhancedMessage,
        } : undefined,
        message: result.success ? 'Message enhanced successfully' : 'Enhancement failed',
        error: result.error,
    };
    res.json(response);
});
exports.sendChatMessage = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { message } = req.body;
    const schoolId = req.params.schoolId;
    const userId = req.user.id;
    if (!message || message.trim().length === 0) {
        throw new types_1.ValidationError('Message is required');
    }
    if (message.length > 1000) {
        throw new types_1.ValidationError('Message is too long (max 1000 characters)');
    }
    const chatRef = (0, firebase_1.getChatMessagesRef)(schoolId);
    const chatDoc = await chatRef.add({
        userId,
        schoolId,
        message: message.trim(),
        status: 'processing',
        createdAt: new Date(),
    });
    try {
        const context = {
            userId,
            schoolId,
            userRole: req.user.role,
        };
        const aiResult = await aiService_1.aiService.processChatMessage(message.trim(), context);
        await chatDoc.update({
            response: aiResult.response,
            intent: aiResult.intent,
            parameters: aiResult.parameters,
            status: aiResult.success ? 'completed' : 'failed',
        });
        const response = {
            success: aiResult.success,
            data: aiResult.success ? {
                response: aiResult.response,
                intent: aiResult.intent,
                parameters: aiResult.parameters,
                actions: aiResult.actions,
            } : undefined,
            message: aiResult.success ? 'Chat message processed successfully' : 'Chat processing failed',
            error: aiResult.error,
        };
        res.json(response);
    }
    catch (error) {
        await chatDoc.update({
            status: 'failed',
            response: 'Üzgünüm, bir hata oluştu.',
        });
        throw error;
    }
});
exports.getChatHistory = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const schoolId = req.params.schoolId;
    const userId = req.user.id;
    const limit = parseInt(req.query.limit) || 50;
    const page = parseInt(req.query.page) || 1;
    const chatRef = (0, firebase_1.getChatMessagesRef)(schoolId);
    const query = chatRef
        .where('userId', '==', userId)
        .orderBy('createdAt', 'desc')
        .limit(limit);
    const snapshot = await query.get();
    const messages = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt.toDate(),
    }));
    const response = {
        success: true,
        data: {
            messages,
            pagination: {
                page,
                limit,
                total: messages.length,
                hasMore: messages.length === limit,
            },
        },
    };
    res.json(response);
});
exports.deleteChatMessage = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const schoolId = req.params.schoolId;
    const messageId = req.params.messageId;
    const userId = req.user.id;
    const chatRef = (0, firebase_1.getChatMessagesRef)(schoolId);
    const messageDoc = await chatRef.doc(messageId).get();
    if (!messageDoc.exists) {
        throw new types_1.ValidationError('Chat message not found');
    }
    const messageData = messageDoc.data();
    if (messageData.userId !== userId && req.user.role !== 'admin') {
        throw new types_1.ValidationError('You can only delete your own messages');
    }
    await messageDoc.ref.delete();
    const response = {
        success: true,
        message: 'Chat message deleted successfully',
    };
    res.json(response);
});
exports.getChatStats = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const schoolId = req.params.schoolId;
    const userId = req.user.id;
    const chatRef = (0, firebase_1.getChatMessagesRef)(schoolId);
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const [totalSnapshot, recentSnapshot, userSnapshot] = await Promise.all([
        chatRef.count().get(),
        chatRef.where('createdAt', '>=', thirtyDaysAgo).count().get(),
        chatRef.where('userId', '==', userId).count().get(),
    ]);
    const response = {
        success: true,
        data: {
            totalMessages: totalSnapshot.data().count,
            recentMessages: recentSnapshot.data().count,
            userMessages: userSnapshot.data().count,
            period: '30 days',
        },
    };
    res.json(response);
});
//# sourceMappingURL=aiController.js.map