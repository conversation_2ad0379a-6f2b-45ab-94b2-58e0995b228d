"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.notFoundHandler = exports.asyncHandler = exports.errorHandler = void 0;
const types_1 = require("../types");
const errorHandler = (error, req, res, next) => {
    console.error('Error:', error);
    if (error instanceof types_1.AppError) {
        return res.status(error.statusCode).json({
            success: false,
            error: error.message,
        });
    }
    return res.status(500).json({
        success: false,
        error: process.env.NODE_ENV === 'production'
            ? 'Something went wrong'
            : error.message,
    });
};
exports.errorHandler = errorHandler;
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
const notFoundHandler = (req, res) => {
    res.status(404).json({
        success: false,
        error: `Route ${req.originalUrl} not found`,
    });
};
exports.notFoundHandler = notFoundHandler;
//# sourceMappingURL=errorHandler.js.map