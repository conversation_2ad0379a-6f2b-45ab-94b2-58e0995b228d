"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const studentController_1 = require("../controllers/studentController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
router.get('/schools/:schoolId/students', auth_1.authenticateToken, auth_1.requireSchoolAccess, studentController_1.getStudents);
router.post('/schools/:schoolId/students', auth_1.authenticateToken, auth_1.requireSchoolAccess, studentController_1.createStudent);
router.post('/schools/:schoolId/students/import', auth_1.authenticateToken, auth_1.requireSchoolAccess, studentController_1.importStudents);
router.get('/schools/:schoolId/students/:studentId', auth_1.authenticateToken, auth_1.requireSchoolAccess, studentController_1.getStudent);
router.put('/schools/:schoolId/students/:studentId', auth_1.authenticateToken, auth_1.requireSchoolAccess, studentController_1.updateStudent);
router.delete('/schools/:schoolId/students/:studentId', auth_1.authenticateToken, auth_1.requireSchoolAccess, studentController_1.deleteStudent);
exports.default = router;
//# sourceMappingURL=students.js.map